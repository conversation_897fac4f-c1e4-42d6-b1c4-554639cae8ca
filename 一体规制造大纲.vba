'===========================================================================
' 模块: 一体制造大纲格式化工具
' 版本: 1.0
' 日期: 2024-03-12
' 作者: 开发团队
' 描述: 用于格式化Excel工作簿中所有工作表，按照一体制造大纲文档格式进行设置
'       主要处理页面布局、边框、字体、页码、工序编号等
'===========================================================================
' 版本历史:
' 1.0 (2024-03-12) - 初始版本，基于流程卡代码改进
'                    1. 添加详细注释说明
'                    2. 优化代码结构和可读性
'                    3. 增加功能回退支持
' 1.1 (2024-03-12) - 添加首页特定单元格内容复制功能
'                    1. 添加E12单元格值获取作为标题前缀
'                    2. 设置首页E8单元格内容为：E12值+制造大纲
'                    3. 设置首页标题为：E12值+制造大纲（首页）+Manufacturing Outline (First Page)
'                    4. 设置工作表二标题为：E12值+制造大纲+Manufacturing Outline
'                    5. 添加第三、第四工作表的标题设置功能
'===========================================================================

' ▼▼▼▼▼ 主格式化处理函数 ▼▼▼▼▼
' 功能：格式化工作簿中所有工作表，设置页面布局、边框、字体等
' 参数：无
' 返回：无
Sub FormatAllSheets() ' 格式化所有工作表的主函数
    Application.ScreenUpdating = False ' 关闭屏幕更新，提高宏执行速度
    Application.Calculation = xlCalculationManual ' 设置为手动计算，提高执行速度

    Dim ws As Worksheet, homeWs As Worksheet ' 声明工作表对象变量
    Dim pageStart As Long, pageEnd As Long ' 声明页面开始和结束行号变量
    Dim pageSize As Long, totalPages As Long ' 声明每页行数和总页数变量
    Dim outerBorderWeight As XlBorderWeight ' 声明外边框粗细变量
    Dim innerBorderWeight As XlBorderWeight ' 声明内边框粗细变量
    Dim headerBorderWeight As XlBorderWeight ' 声明表头边框粗细变量
    Dim dataCheck As Range, hasData As Boolean ' 声明数据检查范围和数据存在标志
    Dim sheetPages() As Long, sheetStarts() As Long ' 声明各工作表页数和起始页码数组
    Dim wsCount As Integer, i As Integer ' 声明工作表数量和循环变量
    Dim processDict As Object, processPageCount As Object, processCurrentPage As Object ' 声明工序字典对象
    Dim currentProcess As String, processNumber As Long ' 声明当前工序和工序编号
    Dim u As Long, currentGlobalPage As Long ' 声明循环变量和当前全局页码
    Dim cell As Range, homeRange1 As Range, homeRange2 As Range ' 声明单元格和区域变量
    Dim homeRange3 As Range, homeRange4 As Range ' 声明首页模板区域
    Dim HasChinese As Boolean ' 声明是否含有中文标志
    Dim templateValue1 As String, templateValue2 As String ' 声明模板值
    Dim templateValue3 As String, templateValue4 As String ' 声明模板值
    Dim mergeRange As Range ' 声明合并单元格范围
    Dim processSubCounter As Object ' 声明工序子计数器
    Dim firstList As Collection ' 声明需要加粗的文本列表
    Dim secondList As Variant ' 声明第二组需要加粗的文本集合
    Dim text As Variant ' 声明文本变量
    Dim titlePrefix As String                     ' 标题前缀（来自E12单元格的值）
    Dim firstPageTitle As String                  ' 首页标题
    Dim sheetTwoTitle As String                   ' 工作表二标题

    pageSize = 45 ' 设置每页行数为45行
    outerBorderWeight = xlMedium ' 设置外边框为中等粗细
    innerBorderWeight = xlThin ' 设置内边框为细线
    headerBorderWeight = xlMedium ' 设置表头边框为中等粗细
    Set homeWs = ThisWorkbook.worksheets(1) ' 设置首页工作表为第一个工作表
    Set processDict = CreateObject("Scripting.Dictionary") ' 创建工序编号字典
    Set processPageCount = CreateObject("Scripting.Dictionary") ' 创建工序页数统计字典
    Set processSubCounter = CreateObject("Scripting.Dictionary") ' 创建工序子计数器字典
    processNumber = 10 ' 初始化工序编号起始值为10

    ThisWorkbook.VBProject.Name = "格式恢复" ' 设置VBA项目名称

    ' ▼▼▼▼▼ 初始化格式化文本列表 ▼▼▼▼▼
    ' 功能：创建需要加粗和斜体处理的文本集合，便于管理和维护
    ' 返回：包含所有文本的字符串数组
    Private Function InitBoldTextList() As Variant
        Dim tempList As New Collection
        ' 时间日期类
        AddText tempList, "出库时间：______年______月______日______时______分"
        AddText tempList, "贮存到期时间：______年______月______日______时______分"
        AddText tempList, "第一次结束时间：______年______月______日______时______分"
        AddText tempList, "第二次开始时间：______年______月______日______时______分"
        AddText tempList, "第三次结束时间：______年______月______日______时______分"
        AddText tempList, "第四次结束时间：______年______月______日______时______分"
        AddText tempList, "第五次结束时间：______年______月______日______时______分"
        AddText tempList, "第六次结束时间：______年______月______日______时______分"
        ' 批号编号类
        AddText tempList, "批  号： ____________  ；卷  号： ____________"
        AddText tempList, "下料机编号：____________"
        AddText tempList, "脱模剂牌号：____________________"
        AddText tempList, "检验程序编号_____________"
        AddText tempList, "无损检测设备编号：_________________ ；无损报告编号：_________________    "
        ' 保压相关
        AddText tempList, "保压开始时间：____________"
        AddText tempList, "保压结束时间：____________"
        AddText tempList, "保压开始真空度：____________"
        AddText tempList, "保压结束真空度：____________"
        ' 铺贴示意图
        AddText tempList, "铺贴示意图1："
        AddText tempList, "铺贴示意图2："
        AddText tempList, "铺贴示意图3："
        AddText tempList, "铺贴示意图4："
        AddText tempList, "铺贴示意图5："
        AddText tempList, "铺贴示意图6："
        AddText tempList, "铺贴示意图7："
        AddText tempList, "热电偶放置示意图："
        AddText tempList, "真空嘴放置示意图："
        AddText tempList, "零件切割示意图:"
        ' 真空测试相关
        AddText tempList, "测试开始时间:_____日_____时_____分，开始时真空度 ________Mpa；"
        AddText tempList, "关闭真空时间:_____日_____时_____分；5分钟后真空度 ________Mpa；"
        AddText tempList, "5分钟之内真空下降________Mpa"
        AddText tempList, "测试开始时间：________________；关闭真空前真空值：_______Mpa(≤-0.092)；"
        AddText tempList, "关闭真空时间：________________；10分钟后真空值：_______Mpa真空泄露_______Mpa/min"
        AddText tempList, "开始时间：____________"
        AddText tempList, "结束时间：____________"
        AddText tempList, "      Mpa"
        ' 固化相关
        AddText tempList, "固化开始时间：________________                固化结束时间：________________"
        AddText tempList, "固化曲线示意图"
        ' 标识相关
        AddText tempList, "标识内容为：产品名称、零件图号、生产订单号，"
        AddText tempList, "标识区域为非贴膜面中心区域。"
        AddText tempList, "标印格式：图号--图号版次--生产批号"
        ' 转换为数组并返回
        InitBoldTextList = CollectionToArray(tempList)
    End Function

    ' ▼▼▼▼▼ 初始化第二组格式化文本列表 ▼▼▼▼▼
    ' 功能：创建第二组需要加粗和斜体处理的文本集合，便于管理和维护
    ' 返回：包含所有文本的字符串数组
    Private Function InitSecondBoldTextList() As Variant
        Dim tempList As New Collection
        ' 检查项
        AddText tempList, "自检："
        AddText tempList, "是□/否□"
        ' 可以方便地添加更多项目
        ' 转换为数组并返回
        InitSecondBoldTextList = CollectionToArray(tempList)
    End Function

    ' ▼▼▼▼▼ 向集合添加文本项 ▼▼▼▼▼
    ' 功能：添加文本到集合中
    ' 参数：coll - 目标集合
    '       text - 要添加的文本
    Private Sub AddText(coll As Collection, text As String)
        coll.Add text
    End Sub

    ' ▼▼▼▼▼ 将集合转换为数组 ▼▼▼▼▼
    ' 功能：将集合转换为数组
    ' 参数：coll - 源集合
    ' 返回：包含集合所有项的数组
    Private Function CollectionToArray(coll As Collection) As Variant
        Dim result() As String
        Dim i As Long
        ReDim result(0 To coll.count - 1)
        For i = 1 To coll.count
            result(i - 1) = coll(i)
        Next i
        CollectionToArray = result
    End Function

    firstList = InitBoldTextList() ' 初始化第一组加粗文本列表
    secondList = InitSecondBoldTextList() ' 初始化第二组加粗文本列表
    wsCount = ThisWorkbook.worksheets.Count ' 获取工作簿中工作表总数
    ReDim sheetPages(1 To wsCount) ' 重新定义工作表页数数组大小
    ReDim sheetStarts(1 To wsCount) ' 重新定义工作表起始页码数组大小
    totalPages = 0 ' 初始化总页数为0
    For i = 1 To wsCount ' 遍历所有工作表
        sheetPages(i) = GetSheetPageCount(ThisWorkbook.worksheets(i), pageSize) ' 计算每个工作表的页数
        sheetStarts(i) = totalPages + 1 ' 计算每个工作表的起始页码
        totalPages = totalPages + sheetPages(i) ' 累加总页数
    Next i
    ' ▼▼▼▼▼ 预存首页模板值 ▼▼▼▼▼
    ' 功能：从首页获取模板值用于后续复制到其他页面
    With homeWs
        templateValue1 = .Range("O4").MergeArea.Cells(1, 1).Value  ' 获取首页O4单元格合并区域的值
        templateValue2 = .Range("E10").MergeArea.Cells(1, 1).Value ' 获取首页E10单元格合并区域的值
        templateValue3 = .Range("R4").MergeArea.Cells(1, 1).Value  ' 获取首页R4单元格合并区域的值
        templateValue4 = .Range("E14").MergeArea.Cells(1, 1).Value ' 获取首页E14单元格合并区域的值

        ' ▼▼▼▼▼ 获取E12单元格的值作为标题前缀 ▼▼▼▼▼
        titlePrefix = .Range("E12").Value
        If IsEmpty(titlePrefix) Then titlePrefix = "" ' 如果E12为空，设为空字符串

        ' ▼▼▼▼▼ 构建首页标题内容 ▼▼▼▼▼
        firstPageTitle = titlePrefix & "制造大纲（首页）" & vbCrLf & "Manufacturing Outline (First Page)"

        ' ▼▼▼▼▼ 设置首页E8合并单元格内容为：E12值+制造大纲 ▼▼▼▼▼
        Dim e8Content As String
        e8Content = titlePrefix & "制造大纲"
        .Range("E8").MergeArea.Cells(1, 1).Value = e8Content
    End With
    Dim ws2 As Worksheet ' 声明工作表2变量
    Set ws2 = ThisWorkbook.worksheets(2) ' 设置ws2为第二个工作表
    pageStart = 1 ' 页面起始行为1
    hasData = True ' 标记有数据
    Do While hasData ' 循环直到没有数据
        pageEnd = pageStart + pageSize - 1 ' 计算页面结束行
        If pageEnd > ws2.Rows.Count Then Exit Do ' 超出行数则退出
        Set dataCheck = ws2.Range("A" & pageStart & ":R" & pageEnd) ' 设置数据检查范围
        hasData = (Application.CountA(dataCheck) > 0) ' 判断范围内是否有数据
        If hasData Then ' 如果有数据
            On Error Resume Next ' 出错继续
            currentProcess = ws2.Range("B" & (pageStart + 9)).MergeArea.Cells(1, 1).Value ' 获取当前工序
            On Error GoTo 0 ' 恢复出错处理
            If currentProcess <> "" Then ' 如果工序不为空
                processPageCount(currentProcess) = processPageCount(currentProcess) + 1 ' 工序页数加1
            End If
            pageStart = pageEnd + 1 ' 下一页起始行
        Else
            Exit Do ' 没有数据则退出
        End If
    Loop
    For Each ws In ThisWorkbook.worksheets ' 遍历所有工作表
        currentGlobalPage = sheetStarts(ws.Index) ' 当前全局页码
        pageStart = 1 ' 页面起始行为1
        hasData = True ' 标记有数据
        If ws.Index = 2 Then ' 如果是第二个工作表
            processDict.RemoveAll ' 清空工序字典
            processSubCounter.RemoveAll ' 清空工序子计数器
            Set processCurrentPage = CreateObject("Scripting.Dictionary") ' 新建工序当前页字典
            For Each Key In processPageCount.Keys ' 遍历所有工序
                processCurrentPage.Add Key, 0 ' 初始化工序当前页为0
                processSubCounter.Add Key, 0 ' 初始化工序子计数为0
            Next
            processNumber = 10 ' 工序编号重置为10
        End If
        With ws.PageSetup ' 设置页面参数
            .Zoom = 100 ' 页面缩放100%
            .FitToPagesWide = 1 ' 一页宽
            .FitToPagesTall = 1 ' 一页高
            ws.Activate ' 激活当前工作表
            ActiveWindow.Zoom = 81 ' 窗口缩放81%
        End With
        ws.Cells.RowHeight = 11.75 ' 设置行高
        ws.Cells.ColumnWidth = 7.13 ' 设置列宽
        Do While hasData ' 主处理循环
            pageEnd = pageStart + pageSize - 1 ' 计算页面结束行
            If pageEnd > ws.Rows.Count Then Exit Do ' 超出行数则退出
            Set dataCheck = ws.Range("A" & pageStart & ":R" & pageEnd) ' 设置数据检查范围
            hasData = (Application.CountA(dataCheck) > 0) ' 判断范围内是否有数据
            If ws.Index = 2 Then ' 如果是第二个工作表
                Set mergeRange = ws.Range("C" & (pageStart + 9) & ":L" & pageEnd) ' 设置合并单元格范围
                For Each cell In mergeRange ' 遍历合并单元格
                    If cell.MergeCells Then ' 如果是合并单元格
                        If cell.MergeArea.Cells(1, 1).Address = cell.Address Then ' 只处理合并区域左上角
                            Dim cellText As String ' 声明单元格文本变量
                            cellText = CStr(cell.Value) ' 获取单元格文本
                            For Each text In firstList ' 遍历第一组加粗内容
                                PartialBold cell, CStr(text) ' 调用加粗函数
                            Next text
                            For Each text In secondList ' 遍历第二组加粗内容
                                PartialBold cell, CStr(text) ' 调用加粗函数
                            Next text
                        End If
                    End If
                Next cell
            End If
            If hasData Then ' 如果有数据
                If Not (ws.Index = 1 And currentGlobalPage = 1) Then ' 非首页时复制模板值
                    SyncTemplateValue ws, pageStart + 3, "A", templateValue1 ' 复制模板值1
                    SyncTemplateValue ws, pageStart + 3, "D", templateValue2 ' 复制模板值2
                    SyncTemplateValue ws, pageStart + 3, "M", templateValue3 ' 复制模板值3
                    SyncTemplateValue ws, pageStart + 3, "O", templateValue4 ' 复制模板值4
                End If
                With ws.Range("A" & pageStart & ":R" & (pageStart + 6)) ' 设置表头字体
                    .Font.Size = 12 ' 字体大小12
                    .Borders.LineStyle = xlNone ' 无边框
                End With
                If pageSize > 7 Then ' 如果每页大于7行
                    ws.Range("A" & (pageStart + 7) & ":R" & pageEnd).Font.Size = 10 ' 设置正文字体大小10
                End If
                Dim headerEnd As Long ' 声明表头结束行变量
                headerEnd = Application.Min(pageStart + 6, pageEnd) ' 计算表头结束行
                If ws.Index = 1 Then ' 如果是首页
                    With ws.Range("E" & pageStart & ":N" & pageStart + 5) ' 设置首页表头字体
                        .Font.Size = 17 ' 字体大小17
                        .Font.Bold = True ' 加粗
                    End With

                    ' ▼▼▼▼▼ 设置首页标题内容（在标题区域中部位置） ▼▼▼▼▼
                    With ws.Range("E" & (pageStart) & ":N" & (pageStart + 5))
                        .MergeCells = True            ' 合并单元格
                        .Value = firstPageTitle       ' 设置标题文本
                    End With
                Else
                    With ws.Range("G" & pageStart & ":L" & pageStart + 6) ' 设置非首页表头字体
                        .Font.Size = 15 ' 字体大小15
                        .Font.Bold = True ' 加粗
                    End With
                End If
                With ws.Range("A" & pageStart & ":R" & pageEnd) ' 设置边框
                    .Borders.LineStyle = xlNone ' 无边框
                    .Borders(xlEdgeLeft).Weight = outerBorderWeight ' 左边框
                    .Borders(xlEdgeTop).Weight = outerBorderWeight ' 上边框
                    .Borders(xlEdgeBottom).Weight = outerBorderWeight ' 下边框
                    .Borders(xlEdgeRight).Weight = outerBorderWeight ' 右边框
                    .Borders(xlInsideHorizontal).Weight = innerBorderWeight ' 内部横线
                    .Borders(xlInsideVertical).Weight = innerBorderWeight ' 内部竖线
                End With
                If ws.Index = 1 Then ' 首页表头增强边框
                    With ws.Range("A" & pageStart & ":R" & (pageStart + 5))
                        .Borders(xlEdgeLeft).Weight = headerBorderWeight ' 左边框
                        .Borders(xlEdgeTop).Weight = headerBorderWeight ' 上边框
                        .Borders(xlEdgeBottom).Weight = headerBorderWeight ' 下边框
                        .Borders(xlEdgeRight).Weight = headerBorderWeight ' 右边框
                    End With
                Else
                    With ws.Range("A" & pageStart & ":R" & (pageStart + 6))
                        .Borders(xlEdgeLeft).Weight = headerBorderWeight ' 左边框
                        .Borders(xlEdgeTop).Weight = headerBorderWeight ' 上边框
                        .Borders(xlEdgeBottom).Weight = headerBorderWeight ' 下边框
                        .Borders(xlEdgeRight).Weight = headerBorderWeight ' 右边框
                    End With
                End If
                If ws.Index > 1 Then ' 非首页页码及工序处理
                    With ws.Range("Q" & pageStart & ":R" & (pageStart + 2)) ' 页码区域
                        .MergeCells = True ' 合并单元格
                        .HorizontalAlignment = xlCenter ' 水平居中
                        .VerticalAlignment = xlCenter ' 垂直居中
                        .Value = "第" & currentGlobalPage & "页 共" & totalPages & "页" ' 设置页码文本
                        .Font.Size = 11 ' 字体大小11
                        .Font.Bold = True ' 加粗
                    End With
                    If ws.Index = 2 Then ' 第二个工作表工序处理
                        ' ▼▼▼▼▼ 构建工作表二标题内容 ▼▼▼▼▼
                        sheetTwoTitle = titlePrefix & "制造大纲" & vbCrLf & "Manufacturing Outline"

                        ' ▼▼▼▼▼ 设置工作表二标题 ▼▼▼▼▼
                        With ws.Range("G" & (pageStart) & ":L" & (pageStart + 6))
                            .MergeCells = True            ' 合并单元格
                            .Value = sheetTwoTitle        ' 设置标题文本
                        End With

                        On Error Resume Next ' 出错继续
                        currentProcess = ws.Range("B" & (pageStart + 9)).MergeArea.Cells(1, 1).Value ' 获取当前工序
                        On Error GoTo 0 ' 恢复出错处理
                        If currentProcess <> "" Then ' 工序不为空
                            processCurrentPage(currentProcess) = processCurrentPage(currentProcess) + 1 ' 工序当前页加1
                            If Not processDict.Exists(currentProcess) Then ' 工序字典不存在则添加
                                processDict.Add currentProcess, processNumber ' 添加工序编号
                                processNumber = processNumber + 10 ' 工序编号加10
                            End If
                            With ws.Range("A" & (pageStart + 9) & ":A" & (pageStart + 44)) ' 工序序号填入
                                .Value = processDict(currentProcess) ' 填入工序编号
                                .HorizontalAlignment = xlCenter ' 水平居中
                                .VerticalAlignment = xlCenter ' 垂直居中
                                .Font.Size = 10 ' 字体大小10
                            End With
                            With ws.Range("Q" & (pageStart + 3) & ":R" & (pageStart + 6)) ' 分工序页码
                                .MergeCells = True ' 合并单元格
                                .HorizontalAlignment = xlCenter ' 水平居中
                                .VerticalAlignment = xlCenter ' 垂直居中
                                .Value = "序" & processDict(currentProcess) & "第" & processCurrentPage(currentProcess) & _
                                       "页 共" & processPageCount(currentProcess) & "页" ' 设置分工序页码文本
                                .Font.Size = 9 ' 字体大小9
                            End With
                            Set mergeRange = ws.Range("C" & (pageStart + 9) & ":L" & pageEnd) ' 处理有颜色合并单元格
                            For Each cell In mergeRange ' 遍历合并单元格
                                If cell.MergeCells Then ' 如果是合并单元格
                                    If cell.MergeArea.Cells(1, 1).Address = cell.Address Then ' 只处理合并区域左上角
                                        If cell.MergeArea.Rows.Count = 2 And _
                                           cell.MergeArea.Columns.Count = 10 And _
                                           cell.Interior.ColorIndex <> xlNone Then ' 满足条件
                                            processSubCounter(currentProcess) = processSubCounter(currentProcess) + 1 ' 分工序计数加1
                                            Dim rawText As String ' 声明原始文本变量
                                            rawText = CStr(cell.Value) ' 获取原始文本
                                            rawText = RemoveExistingNumber(rawText) ' 移除已有编号
                                            cell.Value = processDict(currentProcess) & "-" & _
                                                       processSubCounter(currentProcess) & "." & _
                                                       GetChinesePart(rawText) ' 填入分工序编号及内容
                                            cell.MergeArea.Font.Bold = True ' 加粗
                                        End If
                                    End If
                                End If
                            Next cell
                    End If

                    ' ▼▼▼▼▼ 工序处理 - 第三个工作表的标题设置 ▼▼▼▼▼
                    If ws.Index = 3 Then               ' 仅在第三个工作表执行
                        ' 构建工作表三标题内容
                        sheetTwoTitle = titlePrefix & "制造大纲（附图页）" & vbCrLf & "Manufacturing Outline"

                        ' 设置工作表三标题
                        With ws.Range("G" & (pageStart) & ":L" & (pageStart + 6))
                            .MergeCells = True            ' 合并单元格
                            .Value = sheetTwoTitle        ' 设置标题文本
                        End With
                    End If

                    ' ▼▼▼▼▼ 工序处理 - 第四个工作表的标题设置 ▼▼▼▼▼
                    If ws.Index = 4 Then               ' 仅在第四个工作表执行
                        ' 构建工作表四标题内容
                        sheetTwoTitle = titlePrefix & "制造大纲流程卡" & vbCrLf & "Manufacturing Outline"

                        ' 设置工作表四标题
                        With ws.Range("G" & (pageStart) & ":L" & (pageStart + 6))
                            .MergeCells = True            ' 合并单元格
                            .Value = sheetTwoTitle        ' 设置标题文本
                        End With
                    End If
                End If
            End If
                currentGlobalPage = currentGlobalPage + 1 ' 全局页码加1
                pageStart = pageEnd + 1 ' 下一页起始行
            Else
                Exit Do ' 没有数据则退出
            End If
        Loop
    Next ws
    For Each ws In ThisWorkbook.worksheets ' 统一字体设置
        For Each cell In ws.UsedRange ' 遍历所有单元格
            If Not IsEmpty(cell.Value) Then ' 如果单元格不为空
                ws.Calculate ' 计算工作表
                If cell.HasFormula Then ' 如果单元格有公式
                    Dim displayText As String ' 声明显示文本变量
                    displayText = cell.text ' 获取显示文本
                    HasChinese = False ' 初始化中文标志
                    For u = 1 To Len(displayText) ' 遍历文本
                        If IsChinese(Mid(displayText, u, 1)) Then ' 检查是否有中文
                            HasChinese = True ' 有中文
                            Exit For ' 退出循环
                        End If
                    Next u
                    cell.Font.Name = IIf(HasChinese, "宋体", "宋体") ' 设置字体
                Else
                    cell.Font.Name = "Times New Roman" ' 设置字体
                    For u = 1 To Len(cell.Value) ' 遍历文本
                        If IsChinese(Mid(cell.Value, 1, 1)) Then ' 检查是否有中文
                            cell.Characters(u, 1).Font.Name = "宋体" ' 设置为宋体
                        End If
                    Next u
                End If
            End If
        Next cell
    Next ws
    Application.ScreenUpdating = True ' 恢复屏幕更新
    Application.Calculation = xlCalculationAutomatic ' 恢复自动计算
    MsgBox "格式设置完成！" ' 弹出完成提示
End Sub

' ▼▼▼▼▼ 文本格式化函数 ▼▼▼▼▼
' 功能：在单元格中查找指定文本并将其设为粗体，不改变其他格式
' 参数：cell - 要处理的单元格对象
'       searchText - 要查找并格式化的文本
Sub PartialBold(cell As Range, searchText As String)
    Dim startPos As Long                ' 匹配文本的起始位置
    Dim originalBold As Boolean         ' 原始粗体状态
    Dim compareMethod As VbCompareMethod ' 比较方法

    ' 使用精确匹配（区分大小写和全半角）
    compareMethod = vbBinaryCompare     ' 设置为二进制比较（精确匹配）
    startPos = InStr(1, cell.Value, searchText, compareMethod)  ' 查找第一个匹配位置

    ' 循环查找所有匹配项
    Do While startPos > 0               ' 当找到匹配项时循环
        ' 保存原始格式状态
        originalBold = cell.Characters(startPos, Len(searchText)).Font.Bold

        ' 仅当未设置格式时才设置（避免重复操作）
        If Not originalBold Then        ' 如果文本未格式化
            With cell.Characters(startPos, Len(searchText))
                .Font.Bold = True       ' 设置为粗体
                .Font.Name = cell.Font.Name  ' 保持原字体名称
                .Font.Size = cell.Font.Size  ' 保持原字体大小
            End With
        End If

        ' 继续查找下一个匹配
        startPos = InStr(startPos + Len(searchText), cell.Value, searchText, compareMethod)
    Loop
End Sub

' ▼▼▼▼▼ 首页对应复制 ▼▼▼▼▼
' 功能：将模板值复制到目标工作表的指定位置
' 参数：ws - 目标工作表
'       targetRow - 目标起始行号
'       targetCol - 目标起始列字母
'       templateValue - 要复制的模板值
Private Sub SyncTemplateValue(ws As Worksheet, targetRow As Long, targetCol As String, templateValue As String)
    On Error Resume Next               ' 忽略可能的错误（如单元格不存在或不是合并单元格）
    ' 直接写入目标区域左上角单元格
    With ws.Range(targetCol & targetRow).MergeArea.Cells(1, 1)  ' 获取合并区域的左上角单元格
        .Value = templateValue         ' 设置单元格值
        .HorizontalAlignment = xlCenter ' 水平居中
        .VerticalAlignment = xlCenter  ' 垂直居中
    End With
End Sub

' ▼▼▼▼▼ 智能字体 ▼▼▼▼▼
' 功能：根据单元格内容智能设置字体，中文使用宋体，非中文使用Times New Roman
' 参数：cell - 要设置字体的单元格对象
Private Sub SetCellFont(cell As Range)
    Dim displayText As String          ' 显示文本
    Dim HasChinese As Boolean          ' 是否包含中文标志
    Dim i As Long                      ' 循环计数器

    cell.Font.Name = "Times New Roman" ' 默认设置为Times New Roman

    If cell.HasFormula Then            ' 如果单元格包含公式
        displayText = cell.text        ' 获取显示文本（非公式本身）
        For i = 1 To Len(displayText)  ' 遍历每个字符
            If IsChinese(Mid(displayText, i, 1)) Then  ' 检查是否为中文
                cell.Font.Name = "宋体" ' 如果包含中文，整个单元格设为宋体
                Exit For               ' 找到中文后退出循环
            End If
        Next
    Else                               ' 如果单元格不包含公式
        For i = 1 To Len(cell.Value)   ' 遍历每个字符
            If IsChinese(Mid(cell.Value, i, 1)) Then  ' 检查是否为中文
                cell.Characters(i, 1).Font.Name = "宋体"  ' 仅将中文字符设为宋体
            End If
        Next
    End If
End Sub

' ▼▼▼▼▼ 中文检测函数 ▼▼▼▼▼
' 功能：检测指定字符是否为中文字符（Unicode范围）
' 参数：char - 要检测的单个字符
' 返回：Boolean - 如果是中文返回True，否则返回False
Function IsChinese(ByVal char As String) As Boolean
    Dim code As Long                   ' 字符的Unicode编码
    code = AscW(char)                  ' 获取字符的Unicode编码
    If code < 0 Then code = code + 65536  ' 处理负值编码（扩展Unicode）

    ' 检查是否在中文Unicode范围内
    ' CJK统一汉字范围(4E00-9FFF)
    IsChinese = (code >= &H4E00 And code <= &H9FFF)
End Function

' ▼▼▼▼▼ 清除已有编号格式 ▼▼▼▼▼
' 功能：从字符串中移除开头的工序编号格式（形如"10-1."）
' 参数：str - 要处理的字符串
' 返回：String - 移除编号后的字符串
Function RemoveExistingNumber(str As String) As String
    Dim regex As Object                ' 正则表达式对象
    Set regex = CreateObject("VBScript.RegExp")  ' 创建正则表达式对象
    ' 匹配"数字-数字."格式（如"10-1."）
    With regex
        .Pattern = "^\d+-\d+\."        ' 设置正则表达式模式
        .Global = True                 ' 全局匹配
        .IgnoreCase = True             ' 忽略大小写
    End With
    ' 替换符合模式的内容为空字符串
    RemoveExistingNumber = regex.Replace(str, "")  ' 返回替换后的结果
End Function

' ▼▼▼▼▼ 中文提取函数 ▼▼▼▼▼
' 功能：从字符串中提取第一个中文字符开始到结尾的部分
' 参数：str - 要处理的字符串
' 返回：String - 提取的中文部分，如果没有中文则返回原字符串
Function GetChinesePart(str As String) As String
    Dim i As Long                      ' 循环计数器
    For i = 1 To Len(str)              ' 遍历字符串的每个字符
        If IsChinese(Mid(str, i, 1)) Then  ' 检查当前字符是否为中文
            GetChinesePart = Mid(str, i)   ' 提取从当前位置到结尾的子字符串
            Exit Function                   ' 找到中文后立即返回
        End If
    Next i
    GetChinesePart = str               ' 如果没有找到中文，返回原字符串
End Function

' ▼▼▼▼▼ 工作表页数计算 ▼▼▼▼▼
' 功能：计算指定工作表包含的页数（根据pageSize定义的每页行数）
' 参数：ws - 要计算的工作表
'       pageSize - 每页的行数
' 返回：Long - 工作表的页数
Private Function GetSheetPageCount(ws As Worksheet, pageSize As Long) As Long
    Dim pageStart As Long              ' 页面起始行
    Dim pageEnd As Long                ' 页面结束行
    Dim hasData As Boolean             ' 是否有数据标志
    Dim pageCount As Long              ' 页数计数器
    Dim dataCheck As Range             ' 数据检查范围

    pageStart = 1                      ' 初始化起始行为1
    hasData = True                     ' 初始化有数据标志为True
    pageCount = 0                      ' 初始化页数计数为0

    Do While hasData                   ' 循环直到没有数据
        pageEnd = pageStart + pageSize - 1  ' 计算当前页的结束行
        If pageEnd > ws.Rows.Count Then Exit Do  ' 如果超出工作表最大行数则退出循环

        Set dataCheck = ws.Range("A" & pageStart & ":R" & pageEnd)  ' 设置数据检查范围
        hasData = (Application.CountA(dataCheck) > 0)  ' 检查范围内是否有数据

        If hasData Then                ' 如果有数据
            pageCount = pageCount + 1  ' 页数计数增加
            pageStart = pageEnd + 1    ' 更新下一页的起始行
        Else
            Exit Do                    ' 如果没有数据则退出循环
        End If
    Loop

    GetSheetPageCount = pageCount      ' 返回计算的页数
End Function

