'===========================================================================
' 模块: 一体制造大纲格式化工具
' 版本: 1.0
' 日期: 2024-03-12
' 作者: 开发团队
' 描述: 用于格式化Excel工作簿中所有工作表，按照一体制造大纲文档格式进行设置
'       主要处理页面布局、边框、字体、页码、工序编号等
'===========================================================================
' 版本历史:
' 1.0 (2024-03-12) - 初始版本，基于流程卡代码改进
'                    1. 添加详细注释说明
'                    2. 优化代码结构和可读性
'                    3. 增加功能回退支持
'===========================================================================

' ▼▼▼▼▼ 主格式化处理函数 ▼▼▼▼▼
' 功能：格式化工作簿中所有工作表，设置页面布局、边框、字体等
' 参数：无
' 返回：无
Sub FormatAllSheets() ' 格式化所有工作表的主函数
    Application.ScreenUpdating = False ' 关闭屏幕更新，提高宏执行速度
    Application.Calculation = xlCalculationManual ' 设置为手动计算，提高执行速度

    Dim ws As Worksheet, homeWs As Worksheet ' 声明工作表对象变量
    Dim pageStart As Long, pageEnd As Long ' 声明页面开始和结束行号变量
    Dim pageSize As Long, totalPages As Long ' 声明每页行数和总页数变量
    Dim outerBorderWeight As XlBorderWeight ' 声明外边框粗细变量
    Dim innerBorderWeight As XlBorderWeight ' 声明内边框粗细变量
    Dim headerBorderWeight As XlBorderWeight ' 声明表头边框粗细变量
    Dim dataCheck As Range, hasData As Boolean ' 声明数据检查范围和数据存在标志
    Dim sheetPages() As Long, sheetStarts() As Long ' 声明各工作表页数和起始页码数组
    Dim wsCount As Integer, i As Integer ' 声明工作表数量和循环变量
    Dim processDict As Object, processPageCount As Object, processCurrentPage As Object ' 声明工序字典对象
    Dim currentProcess As String, processNumber As Long ' 声明当前工序和工序编号
    Dim u As Long, currentGlobalPage As Long ' 声明循环变量和当前全局页码
    Dim cell As Range, homeRange1 As Range, homeRange2 As Range ' 声明单元格和区域变量
    Dim homeRange3 As Range, homeRange4 As Range ' 声明首页模板区域
    Dim HasChinese As Boolean ' 声明是否含有中文标志
    Dim templateValue1 As String, templateValue2 As String ' 声明模板值
    Dim templateValue3 As String, templateValue4 As String ' 声明模板值
    Dim mergeRange As Range ' 声明合并单元格范围
    Dim processSubCounter As Object ' 声明工序子计数器
    Dim firstList As Variant, secondList As Variant ' 声明需要加粗的文本列表
    Dim text As Variant ' 声明文本变量

    pageSize = 45 ' 设置每页行数为45行
    outerBorderWeight = xlMedium ' 设置外边框为中等粗细
    innerBorderWeight = xlThin ' 设置内边框为细线
    headerBorderWeight = xlMedium ' 设置表头边框为中等粗细
    Set homeWs = ThisWorkbook.worksheets(1) ' 设置首页工作表为第一个工作表
    Set processDict = CreateObject("Scripting.Dictionary") ' 创建工序编号字典
    Set processPageCount = CreateObject("Scripting.Dictionary") ' 创建工序页数统计字典
    Set processSubCounter = CreateObject("Scripting.Dictionary") ' 创建工序子计数器字典
    processNumber = 10 ' 初始化工序编号起始值为10

    ThisWorkbook.VBProject.Name = "格式恢复" ' 设置VBA项目名称

    firstList = Array( _
        "出库时间：______年______月______日______时______分", "贮存到期时间：______年______月______日______时______分", _
        "剩余操作寿命：____________小时 ；剩余外置寿命：____________小时", "批  号： ____________  ；卷  号： ____________", _
        "下料机编号：____________", "第一次结束时间：______年______月______日______时______分", _
        "脱模剂牌号：____________________", "第二次开始时间：______年______月______日______时______分", _
        "第三次结束时间：______年______月______日______时______分", "第四次结束时间：______年______月______日______时______分", _
        "第五次结束时间：______年______月______日______时______分", "第六次结束时间：______年______月______日______时______分", "保压开始时间：____________", _
        "保压结束时间：____________", "保压开始真空度：____________", _
     "保压结束真空度：____________", "铺贴示意图1：", "铺贴示意图2：", "铺贴示意图3：", _
        "铺贴示意图4：", "铺贴示意图5：", "铺贴示意图6：", "铺贴示意图7：", "测试开始时间:_____日_____时_____分，开始时真空度 ________Mpa；", _
        "关闭真空时间:_____日_____时_____分；5分钟后真空度 ________Mpa；", "5分钟之内真空下降________Mpa", _
        "测试开始时间：________________；关闭真空前真空值：_______Mpa(≤-0.092)；", "关闭真空时间：________________；10分钟后真空值：_______Mpa真空泄露_______Mpa/min", _
        "固化开始时间：________________                固化结束时间：________________", "固化曲线示意图", _
        "标识内容为：产品名称、零件图号、生产订单号，", "标识区域为非贴膜面中心区域。", _
        "检验程序编号_____________", "无损检测设备编号：_________________ ；无损报告编号：_________________    ", _
        "标印格式：图号--图号版次--生产批号") ' 初始化需要加粗的文本列表

    secondList = Array("自检：", "是□/否□") ' 初始化第二组需要加粗的文本集合
    wsCount = ThisWorkbook.worksheets.Count ' 获取工作簿中工作表总数
    ReDim sheetPages(1 To wsCount) ' 重新定义工作表页数数组大小
    ReDim sheetStarts(1 To wsCount) ' 重新定义工作表起始页码数组大小
    totalPages = 0 ' 初始化总页数为0
    For i = 1 To wsCount ' 遍历所有工作表
        sheetPages(i) = GetSheetPageCount(ThisWorkbook.worksheets(i), pageSize) ' 计算每个工作表的页数
        sheetStarts(i) = totalPages + 1 ' 计算每个工作表的起始页码
        totalPages = totalPages + sheetPages(i) ' 累加总页数
    Next i
    With homeWs
        templateValue1 = .Range("O4").MergeArea.Cells(1, 1).Value ' 获取首页O4单元格合并区域的值
        templateValue2 = .Range("E10").MergeArea.Cells(1, 1).Value ' 获取首页E10单元格合并区域的值
        templateValue3 = .Range("R4").MergeArea.Cells(1, 1).Value ' 获取首页R4单元格合并区域的值
        templateValue4 = .Range("E14").MergeArea.Cells(1, 1).Value ' 获取首页E14单元格合并区域的值
    End With
    Dim ws2 As Worksheet ' 声明工作表2变量
    Set ws2 = ThisWorkbook.worksheets(2) ' 设置ws2为第二个工作表
    pageStart = 1 ' 页面起始行为1
    hasData = True ' 标记有数据
    Do While hasData ' 循环直到没有数据
        pageEnd = pageStart + pageSize - 1 ' 计算页面结束行
        If pageEnd > ws2.Rows.Count Then Exit Do ' 超出行数则退出
        Set dataCheck = ws2.Range("A" & pageStart & ":R" & pageEnd) ' 设置数据检查范围
        hasData = (Application.CountA(dataCheck) > 0) ' 判断范围内是否有数据
        If hasData Then ' 如果有数据
            On Error Resume Next ' 出错继续
            currentProcess = ws2.Range("B" & (pageStart + 9)).MergeArea.Cells(1, 1).Value ' 获取当前工序
            On Error GoTo 0 ' 恢复出错处理
            If currentProcess <> "" Then ' 如果工序不为空
                processPageCount(currentProcess) = processPageCount(currentProcess) + 1 ' 工序页数加1
            End If
            pageStart = pageEnd + 1 ' 下一页起始行
        Else
            Exit Do ' 没有数据则退出
        End If
    Loop
    For Each ws In ThisWorkbook.worksheets ' 遍历所有工作表
        currentGlobalPage = sheetStarts(ws.Index) ' 当前全局页码
        pageStart = 1 ' 页面起始行为1
        hasData = True ' 标记有数据
        If ws.Index = 2 Then ' 如果是第二个工作表
            processDict.RemoveAll ' 清空工序字典
            processSubCounter.RemoveAll ' 清空工序子计数器
            Set processCurrentPage = CreateObject("Scripting.Dictionary") ' 新建工序当前页字典
            For Each Key In processPageCount.Keys ' 遍历所有工序
                processCurrentPage.Add Key, 0 ' 初始化工序当前页为0
                processSubCounter.Add Key, 0 ' 初始化工序子计数为0
            Next
            processNumber = 10 ' 工序编号重置为10
        End If
        With ws.PageSetup ' 设置页面参数
            .Zoom = 100 ' 页面缩放100%
            .FitToPagesWide = 1 ' 一页宽
            .FitToPagesTall = 1 ' 一页高
            ws.Activate ' 激活当前工作表
            ActiveWindow.Zoom = 81 ' 窗口缩放81%
        End With
        ws.Cells.RowHeight = 11.75 ' 设置行高
        ws.Cells.ColumnWidth = 7.13 ' 设置列宽
        Do While hasData ' 主处理循环
            pageEnd = pageStart + pageSize - 1 ' 计算页面结束行
            If pageEnd > ws.Rows.Count Then Exit Do ' 超出行数则退出
            Set dataCheck = ws.Range("A" & pageStart & ":R" & pageEnd) ' 设置数据检查范围
            hasData = (Application.CountA(dataCheck) > 0) ' 判断范围内是否有数据
            If ws.Index = 2 Then ' 如果是第二个工作表
                Set mergeRange = ws.Range("C" & (pageStart + 9) & ":L" & pageEnd) ' 设置合并单元格范围
                For Each cell In mergeRange ' 遍历合并单元格
                    If cell.MergeCells Then ' 如果是合并单元格
                        If cell.MergeArea.Cells(1, 1).Address = cell.Address Then ' 只处理合并区域左上角
                            Dim cellText As String ' 声明单元格文本变量
                            cellText = CStr(cell.Value) ' 获取单元格文本
                            For Each text In firstList ' 遍历第一组加粗内容
                                PartialBold cell, CStr(text) ' 调用加粗函数
                            Next text
                            For Each text In secondList ' 遍历第二组加粗内容
                                PartialBold cell, CStr(text) ' 调用加粗函数
                            Next text
                        End If
                    End If
                Next cell
            End If
            If hasData Then ' 如果有数据
                If Not (ws.Index = 1 And currentGlobalPage = 1) Then ' 非首页时复制模板值
                    SyncTemplateValue ws, pageStart + 3, "A", templateValue1 ' 复制模板值1
                    SyncTemplateValue ws, pageStart + 3, "D", templateValue2 ' 复制模板值2
                    SyncTemplateValue ws, pageStart + 3, "M", templateValue3 ' 复制模板值3
                    SyncTemplateValue ws, pageStart + 3, "O", templateValue4 ' 复制模板值4
                End If
                With ws.Range("A" & pageStart & ":R" & (pageStart + 6)) ' 设置表头字体
                    .Font.Size = 12 ' 字体大小12
                    .Borders.LineStyle = xlNone ' 无边框
                End With
                If pageSize > 7 Then ' 如果每页大于7行
                    ws.Range("A" & (pageStart + 7) & ":R" & pageEnd).Font.Size = 10 ' 设置正文字体大小10
                End If
                Dim headerEnd As Long ' 声明表头结束行变量
                headerEnd = Application.Min(pageStart + 6, pageEnd) ' 计算表头结束行
                If ws.Index = 1 Then ' 如果是首页
                    With ws.Range("E" & pageStart & ":N" & pageStart + 5) ' 设置首页表头字体
                        .Font.Size = 17 ' 字体大小17
                        .Font.Bold = True ' 加粗
                    End With
                Else
                    With ws.Range("G" & pageStart & ":L" & pageStart + 6) ' 设置非首页表头字体
                        .Font.Size = 15 ' 字体大小15
                        .Font.Bold = True ' 加粗
                    End With
                End If
                With ws.Range("A" & pageStart & ":R" & pageEnd) ' 设置边框
                    .Borders.LineStyle = xlNone ' 无边框
                    .Borders(xlEdgeLeft).Weight = outerBorderWeight ' 左边框
                    .Borders(xlEdgeTop).Weight = outerBorderWeight ' 上边框
                    .Borders(xlEdgeBottom).Weight = outerBorderWeight ' 下边框
                    .Borders(xlEdgeRight).Weight = outerBorderWeight ' 右边框
                    .Borders(xlInsideHorizontal).Weight = innerBorderWeight ' 内部横线
                    .Borders(xlInsideVertical).Weight = innerBorderWeight ' 内部竖线
                End With
                If ws.Index = 1 Then ' 首页表头增强边框
                    With ws.Range("A" & pageStart & ":R" & (pageStart + 5))
                        .Borders(xlEdgeLeft).Weight = headerBorderWeight ' 左边框
                        .Borders(xlEdgeTop).Weight = headerBorderWeight ' 上边框
                        .Borders(xlEdgeBottom).Weight = headerBorderWeight ' 下边框
                        .Borders(xlEdgeRight).Weight = headerBorderWeight ' 右边框
                    End With
                Else
                    With ws.Range("A" & pageStart & ":R" & (pageStart + 6))
                        .Borders(xlEdgeLeft).Weight = headerBorderWeight ' 左边框
                        .Borders(xlEdgeTop).Weight = headerBorderWeight ' 上边框
                        .Borders(xlEdgeBottom).Weight = headerBorderWeight ' 下边框
                        .Borders(xlEdgeRight).Weight = headerBorderWeight ' 右边框
                    End With
                End If
                If ws.Index > 1 Then ' 非首页页码及工序处理
                    With ws.Range("Q" & pageStart & ":R" & (pageStart + 2)) ' 页码区域
                        .MergeCells = True ' 合并单元格
                        .HorizontalAlignment = xlCenter ' 水平居中
                        .VerticalAlignment = xlCenter ' 垂直居中
                        .Value = "第" & currentGlobalPage & "页 共" & totalPages & "页" ' 设置页码文本
                        .Font.Size = 11 ' 字体大小11
                        .Font.Bold = True ' 加粗
                    End With
                    If ws.Index = 2 Then ' 第二个工作表工序处理
                        On Error Resume Next ' 出错继续
                        currentProcess = ws.Range("B" & (pageStart + 9)).MergeArea.Cells(1, 1).Value ' 获取当前工序
                        On Error GoTo 0 ' 恢复出错处理
                        If currentProcess <> "" Then ' 工序不为空
                            processCurrentPage(currentProcess) = processCurrentPage(currentProcess) + 1 ' 工序当前页加1
                            If Not processDict.Exists(currentProcess) Then ' 工序字典不存在则添加
                                processDict.Add currentProcess, processNumber ' 添加工序编号
                                processNumber = processNumber + 10 ' 工序编号加10
                            End If
                            With ws.Range("A" & (pageStart + 9) & ":A" & (pageStart + 44)) ' 工序序号填入
                                .Value = processDict(currentProcess) ' 填入工序编号
                                .HorizontalAlignment = xlCenter ' 水平居中
                                .VerticalAlignment = xlCenter ' 垂直居中
                                .Font.Size = 10 ' 字体大小10
                            End With
                            With ws.Range("Q" & (pageStart + 3) & ":R" & (pageStart + 6)) ' 分工序页码
                                .MergeCells = True ' 合并单元格
                                .HorizontalAlignment = xlCenter ' 水平居中
                                .VerticalAlignment = xlCenter ' 垂直居中
                                .Value = "序" & processDict(currentProcess) & "第" & processCurrentPage(currentProcess) & _
                                       "页 共" & processPageCount(currentProcess) & "页" ' 设置分工序页码文本
                                .Font.Size = 9 ' 字体大小9
                            End With
                            Set mergeRange = ws.Range("C" & (pageStart + 9) & ":L" & pageEnd) ' 处理有颜色合并单元格
                            For Each cell In mergeRange ' 遍历合并单元格
                                If cell.MergeCells Then ' 如果是合并单元格
                                    If cell.MergeArea.Cells(1, 1).Address = cell.Address Then ' 只处理合并区域左上角
                                        If cell.MergeArea.Rows.Count = 2 And _
                                           cell.MergeArea.Columns.Count = 10 And _
                                           cell.Interior.ColorIndex <> xlNone Then ' 满足条件
                                            processSubCounter(currentProcess) = processSubCounter(currentProcess) + 1 ' 分工序计数加1
                                            Dim rawText As String ' 声明原始文本变量
                                            rawText = CStr(cell.Value) ' 获取原始文本
                                            rawText = RemoveExistingNumber(rawText) ' 移除已有编号
                                            cell.Value = processDict(currentProcess) & "-" & _
                                                       processSubCounter(currentProcess) & "." & _
                                                       GetChinesePart(rawText) ' 填入分工序编号及内容
                                            cell.MergeArea.Font.Bold = True ' 加粗
                                        End If
                                    End If
                                End If
                            Next cell
                    End If
                End If
            End If
                currentGlobalPage = currentGlobalPage + 1 ' 全局页码加1
                pageStart = pageEnd + 1 ' 下一页起始行
            Else
                Exit Do ' 没有数据则退出
            End If
        Loop
    Next ws
    For Each ws In ThisWorkbook.worksheets ' 统一字体设置
        For Each cell In ws.UsedRange ' 遍历所有单元格
            If Not IsEmpty(cell.Value) Then ' 如果单元格不为空
                ws.Calculate ' 计算工作表
                If cell.HasFormula Then ' 如果单元格有公式
                    Dim displayText As String ' 声明显示文本变量
                    displayText = cell.text ' 获取显示文本
                    HasChinese = False ' 初始化中文标志
                    For u = 1 To Len(displayText) ' 遍历文本
                        If IsChinese(Mid(displayText, u, 1)) Then ' 检查是否有中文
                            HasChinese = True ' 有中文
                            Exit For ' 退出循环
                        End If
                    Next u
                    cell.Font.Name = IIf(HasChinese, "宋体", "宋体") ' 设置字体
                Else
                    cell.Font.Name = "Times New Roman" ' 设置字体
                    For u = 1 To Len(cell.Value) ' 遍历文本
                        If IsChinese(Mid(cell.Value, 1, 1)) Then ' 检查是否有中文
                            cell.Characters(u, 1).Font.Name = "宋体" ' 设置为宋体
                        End If
                    Next u
                End If
            End If
        Next cell
    Next ws
    Application.ScreenUpdating = True ' 恢复屏幕更新
    Application.Calculation = xlCalculationAutomatic ' 恢复自动计算
    MsgBox "格式设置完成！" ' 弹出完成提示
End Sub

Sub PartialBold(cell As Range, searchText As String) ' 加粗函数
    Dim startPos As Long ' 声明起始位置变量
    Dim originalBold As Boolean ' 声明原始粗体状态变量
    Dim compareMethod As VbCompareMethod ' 声明比较方法变量
    compareMethod = vbBinaryCompare ' 使用精确匹配
    startPos = InStr(1, cell.Value, searchText, compareMethod) ' 查找匹配位置
    Do While startPos > 0 ' 循环查找所有匹配项
        originalBold = cell.Characters(startPos, Len(searchText)).Font.Bold ' 保存原始粗体状态
        If Not originalBold Then ' 未加粗时才设置
            With cell.Characters(startPos, Len(searchText))
                .Font.Bold = True ' 加粗
                .Font.Name = cell.Font.Name ' 保持原有字体
                .Font.Size = cell.Font.Size ' 保持原有字号
            End With
        End If
        startPos = InStr(startPos + Len(searchText), cell.Value, searchText, compareMethod) ' 查找下一个匹配
    Loop
End Sub

Private Sub SyncTemplateValue(ws As Worksheet, targetRow As Long, targetCol As String, templateValue As String) ' 首页对应复制
    On Error Resume Next ' 出错继续
    With ws.Range(targetCol & targetRow).MergeArea.Cells(1, 1) ' 目标区域左上角单元格
        .Value = templateValue ' 写入模板值
        .HorizontalAlignment = xlCenter ' 水平居中
        .VerticalAlignment = xlCenter ' 垂直居中
    End With
End Sub

Private Sub SetCellFont(cell As Range) ' 智能字体
    Dim displayText As String ' 声明显示文本变量
    Dim HasChinese As Boolean ' 声明中文标志
    Dim i As Long ' 声明循环变量
    cell.Font.Name = "Times New Roman" ' 默认字体
    If cell.HasFormula Then ' 有公式
        displayText = cell.text ' 获取显示文本
        For i = 1 To Len(displayText) ' 遍历文本
            If IsChinese(Mid(displayText, i, 1)) Then ' 检查是否有中文
                cell.Font.Name = "宋体" ' 设置为宋体
                Exit For ' 退出循环
            End If
        Next
    Else
        For i = 1 To Len(cell.Value) ' 遍历文本
            If IsChinese(Mid(cell.Value, i, 1)) Then ' 检查是否有中文
                cell.Characters(i, 1).Font.Name = "宋体" ' 设置为宋体
            End If
        Next
    End If
End Sub

Function IsChinese(ByVal char As String) As Boolean ' 中文检测函数
    Dim code As Long ' 声明编码变量
    code = AscW(char) ' 获取字符编码
    If code < 0 Then code = code + 65536 ' 负数时加65536
    IsChinese = (code >= &H4E00 And code <= &H9FFF) ' 判断是否为中文
End Function

Function RemoveExistingNumber(str As String) As String ' 清除已有编号格式
    Dim regex As Object ' 声明正则对象
    Set regex = CreateObject("VBScript.RegExp") ' 创建正则对象
    With regex
        .Pattern = "^\d+-\d+\."
        .Global = True
        .IgnoreCase = True
    End With
    RemoveExistingNumber = regex.Replace(str, "") ' 替换符合模式的内容
End Function

Function GetChinesePart(str As String) As String ' 中文提取函数
    Dim i As Long ' 声明循环变量
    For i = 1 To Len(str) ' 遍历字符串
        If IsChinese(Mid(str, i, 1)) Then ' 检查是否为中文
            GetChinesePart = Mid(str, i) ' 返回中文部分
            Exit Function ' 退出函数
        End If
    Next i
    GetChinesePart = str ' 没有中文则返回原字符串
End Function

Private Function GetSheetPageCount(ws As Worksheet, pageSize As Long) As Long ' 获取工作表页数
    Dim pageStart As Long, pageEnd As Long ' 声明页面起止变量
    Dim hasData As Boolean, pageCount As Long ' 声明数据存在标志和页数
    Dim dataCheck As Range ' 声明数据检查范围
    pageStart = 1 ' 起始行为1
    hasData = True ' 标记有数据
    pageCount = 0 ' 页数初始化为0
    Do While hasData ' 循环直到没有数据
        pageEnd = pageStart + pageSize - 1 ' 计算结束行
        If pageEnd > ws.Rows.Count Then Exit Do ' 超出行数则退出
        Set dataCheck = ws.Range("A" & pageStart & ":R" & pageEnd) ' 设置数据检查范围
        hasData = (Application.CountA(dataCheck) > 0) ' 判断范围内是否有数据
        If hasData Then ' 有数据
            pageCount = pageCount + 1 ' 页数加1
            pageStart = pageEnd + 1 ' 下一页起始行
        Else
            Exit Do ' 没有数据则退出
        End If
    Loop
    GetSheetPageCount = pageCount ' 返回页数
End Function


    GetSheetPageCount = pageCount
End Function

