Sub FormatAllSheets()
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    
    Dim ws As Worksheet, homeWs As Worksheet
    Dim pageStart As Long, pageEnd As Long
    Dim pageSize As Long, totalPages As Long
    Dim outerBorderWeight As XlBorderWeight
    Dim innerBorderWeight As XlBorderWeight
    Dim headerBorderWeight As XlBorderWeight
    Dim dataCheck As Range, hasData As Boolean
    Dim sheetPages() As Long, sheetStarts() As Long
    Dim wsCount As Integer, i As Integer
    Dim processDict As Object, processPageCount As Object, processCurrentPage As Object
    Dim currentProcess As String, processNumber As Long
    Dim u As Long, currentGlobalPage As Long
    Dim cell As Range, homeRange1 As Range, homeRange2 As Range
    Dim homeRange3 As Range, homeRange4 As Range
    Dim HasChinese As Boolean
    Dim templateValue1 As String, templateValue2 As String
    Dim templateValue3 As String, templateValue4 As String
    Dim mergeRange As Range
    Dim processSubCounter As Object
    Dim firstList As Variant, secondList As Variant
    Dim text As Variant

    
    ' 参数初始化
    pageSize = 45
    outerBorderWeight = xlMedium
    innerBorderWeight = xlThin
    headerBorderWeight = xlMedium
    Set homeWs = ThisWorkbook.worksheets(1)
    Set processDict = CreateObject("Scripting.Dictionary")
    Set processPageCount = CreateObject("Scripting.Dictionary")
    Set processSubCounter = CreateObject("Scripting.Dictionary")
    processNumber = 10
    
    ThisWorkbook.VBProject.Name = "格式恢复"
    
    
    firstList = Array( _
        "出库时间：______年______月______日______时______分", "贮存到期时间：______年______月______日______时______分", _
        "剩余操作寿命：____________小时 ；剩余外置寿命：____________小时", "批  号： ____________  ；卷  号： ____________", _
        "下料机编号：____________", "第一次结束时间：______年______月______日______时______分", _
        "脱模剂牌号：____________________", "第二次开始时间：______年______月______日______时______分", _
        "第三次结束时间：______年______月______日______时______分", "第四次结束时间：______年______月______日______时______分", _
        "第五次结束时间：______年______月______日______时______分", "第六次结束时间：______年______月______日______时______分", "保压开始时间：____________", _
        "保压结束时间：____________", "保压开始真空度：____________", _
     "保压结束真空度：____________", "铺贴示意图1：", "铺贴示意图2：", "铺贴示意图3：", _
        "铺贴示意图4：", "铺贴示意图5：", "铺贴示意图6：", "铺贴示意图7：", "测试开始时间:_____日_____时_____分，开始时真空度 ________Mpa；", _
        "关闭真空时间:_____日_____时_____分；5分钟后真空度 ________Mpa；", "5分钟之内真空下降________Mpa", _
        "测试开始时间：________________；关闭真空前真空值：_______Mpa(≤-0.092)；", "关闭真空时间：________________；10分钟后真空值：_______Mpa真空泄露_______Mpa/min", _
        "固化开始时间：________________                固化结束时间：________________", "固化曲线示意图", _
        "标识内容为：产品名称、零件图号、生产订单号，", "标识区域为非贴膜面中心区域。", _
        "检验程序编号_____________", "无损检测设备编号：_________________ ；无损报告编号：_________________    ", _
        "标印格式：图号--图号版次--生产批号")
    
    secondList = Array("自检：", "是□/否□")
    
    ' 预计算所有工作表的页数和起始页码
    wsCount = ThisWorkbook.worksheets.Count
    ReDim sheetPages(1 To wsCount)
    ReDim sheetStarts(1 To wsCount)
    
    totalPages = 0
    For i = 1 To wsCount
        sheetPages(i) = GetSheetPageCount(ThisWorkbook.worksheets(i), pageSize)
        sheetStarts(i) = totalPages + 1
        totalPages = totalPages + sheetPages(i)
    Next i
    
    ' 预存首页模板值
    With homeWs
        templateValue1 = .Range("O4").MergeArea.Cells(1, 1).Value
        templateValue2 = .Range("E10").MergeArea.Cells(1, 1).Value
        templateValue3 = .Range("R4").MergeArea.Cells(1, 1).Value
        templateValue4 = .Range("E14").MergeArea.Cells(1, 1).Value
    End With
    
    ' 预扫描工作表2获取工序总页数
    Dim ws2 As Worksheet
    Set ws2 = ThisWorkbook.worksheets(2)
    pageStart = 1
    hasData = True
    Do While hasData
        pageEnd = pageStart + pageSize - 1
        If pageEnd > ws2.Rows.Count Then Exit Do
        
        Set dataCheck = ws2.Range("A" & pageStart & ":R" & pageEnd)
        hasData = (Application.CountA(dataCheck) > 0)
        
        If hasData Then
            On Error Resume Next
            currentProcess = ws2.Range("B" & (pageStart + 9)).MergeArea.Cells(1, 1).Value
            On Error GoTo 0
            If currentProcess <> "" Then
                processPageCount(currentProcess) = processPageCount(currentProcess) + 1
            End If
            pageStart = pageEnd + 1
        Else
            Exit Do
        End If
    Loop


    ' 主格式设置循环
    For Each ws In ThisWorkbook.worksheets
        currentGlobalPage = sheetStarts(ws.Index)
        pageStart = 1
        hasData = True
        
        
        ' 初始化工序计数器
        If ws.Index = 2 Then
            processDict.RemoveAll
            processSubCounter.RemoveAll
            Set processCurrentPage = CreateObject("Scripting.Dictionary")
            For Each Key In processPageCount.Keys
                processCurrentPage.Add Key, 0
                processSubCounter.Add Key, 0
            Next
            processNumber = 10
        End If
        
        With ws.PageSetup
            .Zoom = 100
            .FitToPagesWide = 1
            .FitToPagesTall = 1
            ws.Activate
            ActiveWindow.Zoom = 81
        End With
        
        ws.Cells.RowHeight = 11.75
        ws.Cells.ColumnWidth = 7.13
        
        ' 主处理循环
        Do While hasData
            pageEnd = pageStart + pageSize - 1
            If pageEnd > ws.Rows.Count Then Exit Do
            
            Set dataCheck = ws.Range("A" & pageStart & ":R" & pageEnd)
            hasData = (Application.CountA(dataCheck) > 0)
            
            If ws.Index = 2 Then
                Set mergeRange = ws.Range("C" & (pageStart + 9) & ":L" & pageEnd)
                For Each cell In mergeRange
                    If cell.MergeCells Then
                        If cell.MergeArea.Cells(1, 1).Address = cell.Address Then
                            ' 获取合并单元格的完整文本
                            Dim cellText As String
                            cellText = CStr(cell.Value)
                            
                            ' ▼▼▼▼▼ 修改为部分加粗 ▼▼▼▼▼
                            ' 处理一次性查找内容
                            For Each text In firstList
                                PartialBold cell, CStr(text)
                            Next text
                            
                            ' 处理需要持续查找的内容
                            For Each text In secondList
                                PartialBold cell, CStr(text)
                            Next text
                        End If
                    End If
                Next cell
            End If
            
            If hasData Then
                 
                 ' 跨页模板值复制（不改变目标合并状态）
                If Not (ws.Index = 1 And currentGlobalPage = 1) Then
                    ' 参数说明：目标起始行，目标列，模板值
                    SyncTemplateValue ws, pageStart + 3, "A", templateValue1  '原O4:Q6
                    SyncTemplateValue ws, pageStart + 3, "D", templateValue2  '原E10:G11
                    SyncTemplateValue ws, pageStart + 3, "M", templateValue3  '原R4:R6
                    SyncTemplateValue ws, pageStart + 3, "O", templateValue4  '原E14:G15
                End If
                
                 ' 字体分级设置
                With ws.Range("A" & pageStart & ":R" & (pageStart + 6))
                    .Font.Size = 12
                    .Borders.LineStyle = xlNone
                End With
                
                If pageSize > 7 Then
                    ws.Range("A" & (pageStart + 7) & ":R" & pageEnd).Font.Size = 10
                End If
                
                 ' 特殊列加粗
                Dim headerEnd As Long
                headerEnd = Application.Min(pageStart + 6, pageEnd)
                If ws.Index = 1 Then
                    With ws.Range("E" & pageStart & ":N" & pageStart + 5)
                        .Font.Size = 17
                        .Font.Bold = True
                    End With
                Else
                    With ws.Range("G" & pageStart & ":L" & pageStart + 6)
                        .Font.Size = 15
                        .Font.Bold = True
                    End With
                End If
                
                ' 边框设置
                With ws.Range("A" & pageStart & ":R" & pageEnd)
                    .Borders.LineStyle = xlNone
                    .Borders(xlEdgeLeft).Weight = outerBorderWeight
                    .Borders(xlEdgeTop).Weight = outerBorderWeight
                    .Borders(xlEdgeBottom).Weight = outerBorderWeight
                    .Borders(xlEdgeRight).Weight = outerBorderWeight
                    .Borders(xlInsideHorizontal).Weight = innerBorderWeight
                    .Borders(xlInsideVertical).Weight = innerBorderWeight
                End With
                
                 ' 表头增强边框
                If ws.Index = 1 Then
                    With ws.Range("A" & pageStart & ":R" & (pageStart + 5))
                        .Borders(xlEdgeLeft).Weight = headerBorderWeight
                        .Borders(xlEdgeTop).Weight = headerBorderWeight
                        .Borders(xlEdgeBottom).Weight = headerBorderWeight
                        .Borders(xlEdgeRight).Weight = headerBorderWeight
                    End With
                Else
                    With ws.Range("A" & pageStart & ":R" & (pageStart + 6))
                        .Borders(xlEdgeLeft).Weight = headerBorderWeight
                        .Borders(xlEdgeTop).Weight = headerBorderWeight
                        .Borders(xlEdgeBottom).Weight = headerBorderWeight
                        .Borders(xlEdgeRight).Weight = headerBorderWeight
                    End With
                End If
                
                
                ' 页码及工序处理
                If ws.Index > 1 Then
                    ' 全局页码
                    With ws.Range("Q" & pageStart & ":R" & (pageStart + 2))
                        .MergeCells = True
                        .HorizontalAlignment = xlCenter
                        .VerticalAlignment = xlCenter
                        .Value = "第" & currentGlobalPage & "页 共" & totalPages & "页"
                        .Font.Size = 11
                        .Font.Bold = True
                    End With
                    
                    ' 工序处理
                    If ws.Index = 2 Then
                        On Error Resume Next
                        currentProcess = ws.Range("B" & (pageStart + 9)).MergeArea.Cells(1, 1).Value
                        On Error GoTo 0
                        If currentProcess <> "" Then
                            processCurrentPage(currentProcess) = processCurrentPage(currentProcess) + 1
                            If Not processDict.Exists(currentProcess) Then
                                processDict.Add currentProcess, processNumber
                                processNumber = processNumber + 10
                            End If
                            '工序序号填入
                            With ws.Range("A" & (pageStart + 9) & ":A" & (pageStart + 44))
                                .Value = processDict(currentProcess)
                                .HorizontalAlignment = xlCenter
                                .VerticalAlignment = xlCenter
                                .Font.Size = 10
                            End With
                            
                            '分工序页码
                            With ws.Range("Q" & (pageStart + 3) & ":R" & (pageStart + 6))
                                .MergeCells = True
                                .HorizontalAlignment = xlCenter
                                .VerticalAlignment = xlCenter
                                .Value = "序" & processDict(currentProcess) & "第" & processCurrentPage(currentProcess) & _
                                       "页 共" & processPageCount(currentProcess) & "页"
                                .Font.Size = 9
                            End With
                            
                            ' 处理有颜色合并单元格
                            Set mergeRange = ws.Range("C" & (pageStart + 9) & ":L" & pageEnd)
                            For Each cell In mergeRange
                                If cell.MergeCells Then
                                    If cell.MergeArea.Cells(1, 1).Address = cell.Address Then
                                        If cell.MergeArea.Rows.Count = 2 And _
                                           cell.MergeArea.Columns.Count = 10 And _
                                           cell.Interior.ColorIndex <> xlNone Then
                                           
                                            processSubCounter(currentProcess) = processSubCounter(currentProcess) + 1
                                            Dim rawText As String
                                            rawText = CStr(cell.Value)
                                            rawText = RemoveExistingNumber(rawText)
                                            
                                            '分工序填入
                                            cell.Value = processDict(currentProcess) & "-" & _
                                                       processSubCounter(currentProcess) & "." & _
                                                       GetChinesePart(rawText)
                                            'cell.MergeArea.Font.Name = "宋体"
                                            'cell.MergeArea.Font.Size = 10
                                            cell.MergeArea.Font.Bold = True
                                        End If
                                    End If
                                End If
                            Next cell
                    End If
                End If
                
            End If
                currentGlobalPage = currentGlobalPage + 1
                pageStart = pageEnd + 1
            Else
                Exit Do
            End If
        Loop
    Next ws

    ' 统一字体设置
    For Each ws In ThisWorkbook.worksheets
        For Each cell In ws.UsedRange
            If Not IsEmpty(cell.Value) Then
                ws.Calculate
                If cell.HasFormula Then
                    Dim displayText As String
                    displayText = cell.text
                    HasChinese = False
                    For u = 1 To Len(displayText)
                        If IsChinese(Mid(displayText, u, 1)) Then
                            HasChinese = True
                            Exit For
                        End If
                    Next u
                    cell.Font.Name = IIf(HasChinese, "宋体", "宋体")
                Else
                    cell.Font.Name = "Times New Roman"
                    For u = 1 To Len(cell.Value)
                        If IsChinese(Mid(cell.Value, u, 1)) Then
                            cell.Characters(u, 1).Font.Name = "宋体"
                        End If
                    Next u
                End If
            End If
        Next cell
    Next ws

    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    MsgBox "格式设置完成！"
End Sub

' ▼▼▼▼▼ 加粗函数 ▼▼▼▼▼
Sub PartialBold(cell As Range, searchText As String)
    Dim startPos As Long
    Dim originalBold As Boolean
    Dim compareMethod As VbCompareMethod
    
    ' 使用精确匹配（区分大小写和全半角）
    compareMethod = vbBinaryCompare
    startPos = InStr(1, cell.Value, searchText, compareMethod)
    
    ' 循环查找所有匹配项
    Do While startPos > 0
        ' 保存原始粗体状态
        originalBold = cell.Characters(startPos, Len(searchText)).Font.Bold
        
        ' 仅当未加粗时才设置（避免重复操作）
        If Not originalBold Then
            With cell.Characters(startPos, Len(searchText))
                .Font.Bold = True
                ' 保持原有字体设置
                .Font.Name = cell.Font.Name
                .Font.Size = cell.Font.Size
            End With
        End If
        
        ' 继续查找下一个匹配
        startPos = InStr(startPos + Len(searchText), cell.Value, searchText, compareMethod)
    Loop
End Sub

' ▼▼▼▼▼ 首页对应复制 ▼▼▼▼▼
Private Sub SyncTemplateValue(ws As Worksheet, targetRow As Long, targetCol As String, templateValue As String)
    On Error Resume Next
    ' 直接写入目标区域左上角单元格
    With ws.Range(targetCol & targetRow).MergeArea.Cells(1, 1)
        .Value = templateValue
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With
End Sub

' ▼▼▼▼▼ 智能字体 ▼▼▼▼▼
Private Sub SetCellFont(cell As Range)
    Dim displayText As String
    Dim HasChinese As Boolean
    Dim i As Long
    
    cell.Font.Name = "Times New Roman"
    
    If cell.HasFormula Then
        displayText = cell.text
        For i = 1 To Len(displayText)
            If IsChinese(Mid(displayText, i, 1)) Then
                cell.Font.Name = "宋体"
                Exit For
            End If
        Next
    Else
        For i = 1 To Len(cell.Value)
            If IsChinese(Mid(cell.Value, i, 1)) Then
                cell.Characters(i, 1).Font.Name = "宋体"
            End If
        Next
    End If
End Sub

' ▼▼▼▼▼ 中文检测函数 ▼▼▼▼▼
Function IsChinese(ByVal char As String) As Boolean
    Dim code As Long
    code = AscW(char)
    If code < 0 Then code = code + 65536
    IsChinese = (code >= &H4E00 And code <= &H9FFF)
End Function

' ▼▼▼▼▼ 清除已有编号格式 ▼▼▼▼▼
Function RemoveExistingNumber(str As String) As String
    Dim regex As Object
    Set regex = CreateObject("VBScript.RegExp")
    ' 匹配"数字-数字."格式
    With regex
        .Pattern = "^\d+-\d+\."
        .Global = True
        .IgnoreCase = True
    End With
    ' 替换符合模式的内容
    RemoveExistingNumber = regex.Replace(str, "")
End Function

' ▼▼▼▼▼ 中文提取函数 ▼▼▼▼▼
Function GetChinesePart(str As String) As String
    Dim i As Long
    For i = 1 To Len(str)
        If IsChinese(Mid(str, i, 1)) Then
            GetChinesePart = Mid(str, i)
            Exit Function
        End If
    Next i
    GetChinesePart = str
End Function

Private Function GetSheetPageCount(ws As Worksheet, pageSize As Long) As Long
    Dim pageStart As Long, pageEnd As Long
    Dim hasData As Boolean, pageCount As Long
    Dim dataCheck As Range
    
    pageStart = 1
    hasData = True
    pageCount = 0
    
    Do While hasData
        pageEnd = pageStart + pageSize - 1
        If pageEnd > ws.Rows.Count Then Exit Do
        
        Set dataCheck = ws.Range("A" & pageStart & ":R" & pageEnd)
        hasData = (Application.CountA(dataCheck) > 0)
        
        If hasData Then
            pageCount = pageCount + 1
            pageStart = pageEnd + 1
        Else
            Exit Do
        End If
    Loop
    
    GetSheetPageCount = pageCount
End Function

