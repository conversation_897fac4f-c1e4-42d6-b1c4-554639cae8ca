
'===========================================================================
' 模块: 格式恢复工具
' 版本: 2.3
' 日期: 2023-12-07
' 作者: 开发团队
' 描述: 用于格式化Excel工作簿中所有工作表，按照特定的工艺流程文档格式进行设置
'       主要处理页面布局、边框、字体、页码、工序编号等
'===========================================================================
' 版本历史:
' 1.0 (2023-05-10) - 初始版本
' 1.1 (2023-10-20) - 1. 统一所有工作表表头为6行
'                    2. 优化表头处理逻辑，移除冗余代码
'                    3. 保持首页和其他页面不同的列标题样式
'                    4. 更新相关注释以提高代码可读性
' 1.2 (2023-10-25) - 1. 添加首页和工作表二表头标题区域内容设置
'                    2. 首页标题设置为"E12值+加工工艺指令（首页）+Assembly Order (First Page)"
'                    3. 工作表二标题设置为"E12值+加工工艺指令（当前工序名+页）+Assembly Order"
'                    4. 优化标题区域的合并单元格范围，避免重复设置格式
' 1.3 (2023-10-26) - 1. 优化加粗文本列表的定义和维护方式
'                    2. 使用函数初始化文本列表，提高代码可读性和可维护性
' 1.4 (2023-10-27) - 1. 增强文本格式化功能，添加斜体样式
'                    2. 修改PartialBold函数，使其能同时设置文本为粗体和斜体
' 1.5 (2023-11-15) - 1. 修改工序处理逻辑，B列改为每两行合并为一个单元格
'                    2. 简化工序序号填写功能，移除工序页码计算
'                    3. 移除子工序加粗和编号功能
'                    4. 优化代码结构，提高效率
' 1.6 (2023-11-16) - 1. 修复工序序号分配逻辑，确保只有B列有数据时才分配序号
'                    2. 优化序号累加机制，避免空白行获取序号
' 1.7 (2023-11-17) - 1. 添加在分配序号前清空A列内容的功能
'                    2. 优化性能，减少不必要的操作
' 1.8 (2023-11-20) - 1. 添加新表头区域识别功能，以"指令编号/Order No."为标记
'                    2. 支持统计工作表二中新表头区域的数量
' 1.9 (2023-11-21) - 1. 移除临时调试功能，包括表头高亮显示和数据行颜色填充
'                    2. 优化代码结构，保留核心数据处理功能
' 2.0 (2023-11-23) - 1. 优化数据行平衡逻辑，确保在移动数据行时保持原有相对顺序
'                    2. 修改移动数据行的方式，确保数据内容不会被打乱
'                    3. 修复在创建新表头时可能导致的数据错位问题
' 2.1 (2023-11-25) - 1. 修复末尾数据组处理逻辑，解决新表头创建后溢出数据行位置变化的问题
'                    2. 优化ProcessLastGroup函数，确保正确处理新表头插入后的数据行移动
'                    3. 修复多次运行时数据行补足问题
' 2.2 (2023-11-28) - 1. 添加检测和删除空白数据行的功能
'                    2. 优化数据补足行生成，确保清除文本内容
'                    3. 添加调整分页预览线框功能，确保显示完整页面
' 2.3 (2023-12-07) - 1. 添加首页E8合并单元格内容设置功能
'                    2. 首页E8内容设置为：E12值+加工工艺指令
' 2.4 (2024-03-12) - 1. 增加工作表2中检验序识别功能
'                    2. 实现检验序特殊编号规则：检验序相对非检验序增量为5
'                    3. 确保检验序序号个位数一定是5
'                    4. 优化工序编号分配逻辑，支持检验序和非检验序不同增量规则
'===========================================================================
' ▼▼▼▼▼ 初始化检验序关键词列表 ▼▼▼▼▼
' 功能：创建用于识别检验序的关键词集合
' 返回：包含所有检验序关键词的字符串数组
Private Function InitInspectionKeywords() As Variant
    Dim tempList As New Collection
    
    ' 添加常见检验序关键词
    AddText tempList, "检验"
    
    ' 转换为数组并返回
    InitInspectionKeywords = CollectionToArray(tempList)
End Function

' ▼▼▼▼▼ 检查是否为检验序 ▼▼▼▼▼
' 功能：检查工序名称是否包含检验序关键词
' 参数：processName - 工序名称
' 返回：Boolean - 如果是检验序返回True，否则返回False
Private Function IsInspectionProcess(processName As String) As Boolean
    Dim keywords As Variant
    Dim keyword As Variant
    
    ' 初始化为非检验序
    IsInspectionProcess = False
    
    ' 如果工序名称为空，直接返回False
    If IsEmpty(processName) Or Trim(processName) = "" Then
        Exit Function
    End If
    
    ' 获取检验序关键词列表
    keywords = InitInspectionKeywords()
    
    ' 检查工序名称是否包含任何检验序关键词
    For Each keyword In keywords
        If InStr(1, processName, CStr(keyword), vbTextCompare) > 0 Then
            IsInspectionProcess = True
            Exit Function
        End If
    Next keyword
End Function

' ▼▼▼▼▼ 初始化格式化文本列表 ▼▼▼▼▼
' 功能：创建需要加粗和斜体处理的文本集合，便于管理和维护
' 返回：包含所有文本的字符串数组
Private Function InitBoldTextList() As Variant
    Dim tempList As New Collection
    
    ' 时间日期类
    AddText tempList, "出库时间：______年______月______日______时______分"
    AddText tempList, "贮存到期时间：______年______月______日______时______分"
    AddText tempList, "第一次结束时间：______年______月______日______时______分"
    AddText tempList, "第二次开始时间：______年______月______日______时______分"
    AddText tempList, "第三次结束时间：______年______月______日______时______分"
    AddText tempList, "第四次结束时间：______年______月______日______时______分"
    AddText tempList, "第五次结束时间：______年______月______日______时______分"
    AddText tempList, "第六次结束时间：______年______月______日______时______分"
    
    ' 批号编号类
    AddText tempList, "批  号： ____________  ；卷  号： ____________"
    AddText tempList, "下料机编号：____________"
    AddText tempList, "脱模剂牌号：____________________"
    AddText tempList, "检验程序编号_____________"
    AddText tempList, "无损检测设备编号：_________________ ；无损报告编号：_________________    "
    
    ' 保压相关
    AddText tempList, "保压开始时间：____________"
    AddText tempList, "保压结束时间：____________"
    AddText tempList, "保压开始真空度：____________"
    AddText tempList, "保压结束真空度：____________"
    
    ' 铺贴示意图
    AddText tempList, "铺贴示意图1："
    AddText tempList, "铺贴示意图2："
    AddText tempList, "铺贴示意图3："
    AddText tempList, "铺贴示意图4："
    AddText tempList, "铺贴示意图5："
    AddText tempList, "铺贴示意图6："
    AddText tempList, "铺贴示意图7："
    AddText tempList, "热电偶放置示意图："
    AddText tempList, "真空嘴放置示意图："
    AddText tempList, "零件切割示意图:"
    
    ' 真空测试相关
    AddText tempList, "测试开始时间:_____日_____时_____分，开始时真空度 ________Mpa；"
    AddText tempList, "关闭真空时间:_____日_____时_____分；5分钟后真空度 ________Mpa；"
    AddText tempList, "5分钟之内真空下降________Mpa"
    AddText tempList, "测试开始时间：________________；关闭真空前真空值：_______Mpa(≤-0.092)；"
    AddText tempList, "关闭真空时间：________________；10分钟后真空值：_______Mpa真空泄露_______Mpa/min"
    AddText tempList, "开始时间：____________"
    AddText tempList, "结束时间：____________"
    AddText tempList, "      Mpa"
    
    ' 固化相关
    AddText tempList, "固化开始时间：________________                固化结束时间：________________"
    AddText tempList, "固化曲线示意图"
    
    ' 标识相关
    AddText tempList, "标识内容为：产品名称、零件图号、生产订单号，"
    AddText tempList, "标识区域为非贴膜面中心区域。"
    AddText tempList, "标印格式：图号--图号版次--生产批号"
    
    ' 转换为数组并返回
    InitBoldTextList = CollectionToArray(tempList)
End Function

' ▼▼▼▼▼ 初始化第二组格式化文本列表 ▼▼▼▼▼
' 功能：创建第二组需要加粗和斜体处理的文本集合，便于管理和维护
' 返回：包含所有文本的字符串数组
Private Function InitSecondBoldTextList() As Variant
    Dim tempList As New Collection
    
    ' 检查项
    AddText tempList, "自检："
    AddText tempList, "是□/否□"
    
    ' 可以方便地添加更多项目
    
    ' 转换为数组并返回
    InitSecondBoldTextList = CollectionToArray(tempList)
End Function

' ▼▼▼▼▼ 向集合添加文本项 ▼▼▼▼▼
' 功能：添加文本到集合中
' 参数：coll - 目标集合
'       text - 要添加的文本
Private Sub AddText(coll As Collection, text As String)
    coll.Add text
End Sub

' ▼▼▼▼▼ 将集合转换为数组 ▼▼▼▼▼
' 功能：将集合转换为数组
' 参数：coll - 源集合
' 返回：包含集合所有项的数组
Private Function CollectionToArray(coll As Collection) As Variant
    Dim result() As String
    Dim i As Long
    
    ReDim result(0 To coll.count - 1)
    
    For i = 1 To coll.count
        result(i - 1) = coll(i)
    Next i
    
    CollectionToArray = result
End Function

Sub FormatAllSheets()
    ' 关闭屏幕更新和设置手动计算，提高宏执行速度
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    
    ' 声明所有变量
    Dim ws As Worksheet, homeWs As Worksheet      ' 工作表对象
    Dim pageStart As Long, pageEnd As Long        ' 页面开始和结束行号
    Dim pageSize As Long, totalPages As Long      ' 每页行数和总页数
    Dim outerBorderWeight As XlBorderWeight       ' 外边框粗细
    Dim innerBorderWeight As XlBorderWeight       ' 内边框粗细
    Dim headerBorderWeight As XlBorderWeight      ' 表头边框粗细
    Dim dataCheck As Range, hasData As Boolean    ' 数据检查范围和是否有数据标志
    Dim sheetPages() As Long, sheetStarts() As Long ' 各工作表页数和起始页码数组
    Dim wsCount As Integer, i As Integer          ' 工作表数量和循环变量
    Dim processDict As Object                     ' 工序字典对象
    Dim currentProcess As String, processNumber As Long ' 当前工序和工序编号
    Dim u As Long, currentGlobalPage As Long      ' 循环变量和当前全局页码
    Dim cell As Range, homeRange1 As Range, homeRange2 As Range ' 单元格和区域变量
    Dim homeRange3 As Range, homeRange4 As Range  ' 首页模板区域
    Dim HasChinese As Boolean                     ' 是否含有中文标志
    Dim templateValue1 As String, templateValue2 As String ' 模板值
    Dim templateValue3 As String, templateValue4 As String ' 模板值
    Dim mergeRange As Range                       ' 合并单元格范围
    Dim firstList As Variant, secondList As Variant ' 需要加粗和斜体的文本列表
    Dim text As Variant                           ' 文本变量
    Dim titlePrefix As String                     ' 标题前缀（来自E12单元格的值）
    Dim firstPageTitle As String                  ' 首页标题
    Dim sheetTwoTitle As String                   ' 工作表二标题
    Dim row As Long                               ' 行号变量
    Dim isInspection As Boolean                   ' 是否为检验序标志
    
    ' 参数初始化
    pageSize = 45                       ' 设置每页行数为45行
    outerBorderWeight = xlMedium        ' 设置外边框为中等粗细
    innerBorderWeight = xlThin          ' 设置内边框为细线
    headerBorderWeight = xlMedium       ' 设置表头边框为中等粗细
    Set homeWs = ThisWorkbook.Worksheets(1)  ' 设置首页工作表为第一个工作表
    Set processDict = CreateObject("Scripting.Dictionary")      ' 创建工序编号字典
    processNumber = 10                  ' 初始化工序编号起始值为10
    
    ThisWorkbook.VBProject.Name = "格式恢复"  ' 设置VBA项目名称
    
    
    ' 初始化需要加粗和斜体的文本列表
    firstList = InitBoldTextList()      ' 使用函数初始化第一组格式化文本
    secondList = InitSecondBoldTextList() ' 使用函数初始化第二组格式化文本
    
    ' 预计算所有工作表的页数和起始页码
    wsCount = ThisWorkbook.Worksheets.count          ' 获取工作簿中工作表总数
    ReDim sheetPages(1 To wsCount)                   ' 重新定义工作表页数数组大小
    ReDim sheetStarts(1 To wsCount)                  ' 重新定义工作表起始页码数组大小
    
    totalPages = 0                                   ' 初始化总页数为0
    For i = 1 To wsCount                             ' 遍历所有工作表
        sheetPages(i) = GetSheetPageCount(ThisWorkbook.Worksheets(i), pageSize)  ' 计算每个工作表的页数
        sheetStarts(i) = totalPages + 1              ' 计算每个工作表的起始页码
        totalPages = totalPages + sheetPages(i)      ' 累加总页数
    Next i
    
    ' 预存首页模板值（从首页获取模板值用于后续复制）
    With homeWs
        templateValue1 = .Range("O4").MergeArea.Cells(1, 1).Value  ' 获取首页O4单元格合并区域的值
        templateValue2 = .Range("E10").MergeArea.Cells(1, 1).Value ' 获取首页E10单元格合并区域的值
        templateValue3 = .Range("R4").MergeArea.Cells(1, 1).Value  ' 获取首页R4单元格合并区域的值
        templateValue4 = .Range("E14").MergeArea.Cells(1, 1).Value ' 获取首页E14单元格合并区域的值
        
        ' 获取E12单元格的值作为标题前缀
        titlePrefix = .Range("E12").Value
        If IsEmpty(titlePrefix) Then titlePrefix = "" ' 如果E12为空，设为空字符串
        
        ' 构建首页标题内容
        firstPageTitle = titlePrefix & "加工工艺指令（首页）" & vbCrLf & "Assembly Order (First Page)"
        
        ' 设置首页E8合并单元格内容为：E12值+加工工艺指令
        Dim e8Content As String
        e8Content = titlePrefix & "加工工艺指令"
        .Range("E8").MergeArea.Cells(1, 1).Value = e8Content
    End With
    

    ' 主格式设置循环（遍历所有工作表进行格式设置）
    For Each ws In ThisWorkbook.Worksheets         ' 遍历每个工作表
        currentGlobalPage = sheetStarts(ws.Index)  ' 设置当前全局页码为该工作表的起始页码
        pageStart = 1                              ' 设置页面起始行为1
        hasData = True                             ' 初始化有数据标志为True
        
        
        ' 初始化工序计数器（只在第二个工作表执行）
        If ws.Index = 2 Then                       ' 如果是第二个工作表（工序表）
            processDict.RemoveAll                  ' 清空工序编号字典
            processNumber = 10                     ' 重置工序编号起始值为10
        End If
        
        ' 设置页面打印和显示属性
        With ws.PageSetup
            .Zoom = 100                    ' 设置打印缩放比例为100%
            .FitToPagesWide = 1            ' 调整为一页宽
            .FitToPagesTall = 1            ' 调整为一页高
            ws.Activate                    ' 激活当前工作表
            ActiveWindow.Zoom = 81         ' 设置屏幕显示缩放比例为81%
        End With
        
        ' 统一设置行高和列宽
        ws.Cells.RowHeight = 11.75         ' 设置所有行高为11.75磅
        ws.Cells.ColumnWidth = 7.13        ' 设置所有列宽为7.13标准宽度
        
        ' 主处理循环（处理每个工作表中的每一页数据）
        Do While hasData                           ' 循环直到没有数据
            pageEnd = pageStart + pageSize - 1     ' 计算当前页的结束行
            If pageEnd > ws.Rows.count Then Exit Do ' 如果超出工作表最大行数则退出循环
            
            Set dataCheck = ws.Range("A" & pageStart & ":R" & pageEnd) ' 设置数据检查范围
            hasData = (Application.CountA(dataCheck) > 0) ' 检查范围内是否有数据
            
            ' 第二个工作表（工序表）特殊处理 - 文本加粗
            If ws.Index = 2 Then                    ' 如果是第二个工作表
                Set mergeRange = ws.Range("C" & (pageStart + 9) & ":L" & pageEnd) ' 设置处理范围C列到L列
                For Each cell In mergeRange          ' 遍历范围内的每个单元格
                    If cell.MergeCells Then          ' 如果是合并单元格
                        ' 只处理合并区域的左上角单元格（避免重复处理）
                        If cell.MergeArea.Cells(1, 1).Address = cell.Address Then
                            ' 获取合并单元格的完整文本
                            Dim cellText As String
                            cellText = CStr(cell.Value)
                            
                            ' ▼▼▼▼▼ 修改为部分加粗 ▼▼▼▼▼
                            ' 处理一次性查找内容（处理第一组需要加粗的文本）
                            For Each text In firstList
                                PartialBold cell, CStr(text) ' 对匹配的文本部分进行加粗
                            Next text
                            
                            ' 处理需要持续查找的内容（处理第二组需要加粗的文本）
                            For Each text In secondList
                                PartialBold cell, CStr(text) ' 对匹配的文本部分进行加粗
                            Next text
                        End If
                    End If
                Next cell
            End If
            
            ' 如果页面有数据，进行格式化处理
            If hasData Then
                 
                 ' 跨页模板值复制（不改变目标合并状态）- 将首页的特定值复制到其他页面对应位置
                If Not (ws.Index = 1 And currentGlobalPage = 1) Then  ' 除了第一页外的所有页面
                    ' 参数说明：目标起始行，目标列，模板值
                    SyncTemplateValue ws, pageStart + 3, "A", templateValue1  '原O4:Q6 位置的值
                    SyncTemplateValue ws, pageStart + 3, "D", templateValue2  '原E10:G11 位置的值
                    SyncTemplateValue ws, pageStart + 3, "M", templateValue3  '原R4:R6 位置的值
                    SyncTemplateValue ws, pageStart + 3, "O", templateValue4  '原E14:G15 位置的值
                End If
                
                 ' 字体分级设置 - 设置不同区域的字体大小
                With ws.Range("A" & pageStart & ":R" & (pageStart + 5))  ' 表头区域（前6行）
                    .Font.Size = 11                   ' 设置字体大小为11
                End With
                
                ' 如果页面大于6行，设置正文区域的字体大小
                If pageSize > 6 Then
                    ws.Range("A" & (pageStart + 6) & ":R" & pageEnd).Font.Size = 10  ' 正文区域字体大小为10
                End If
                
                 ' 特殊列加粗 - 为所有工作表设置标题区域（统一6行表头）
                Dim headerEnd As Long
                headerEnd = Application.Min(pageStart + 5, pageEnd)  ' 计算表头结束行（防止页面不足6行）
                
                ' 根据工作表索引选择合适的标题列范围和字体大小
                If ws.Index = 1 Then                  ' 如果是第一个工作表（首页）
                    With ws.Range("E" & pageStart & ":N" & pageStart + 5)  ' 设置E-N列标题区域
                        .Font.Size = 17               ' 字体大小设为17（首页）
                        .Font.Bold = True             ' 设置为粗体
                    End With
                    
                    ' 设置首页标题内容（在标题区域中部位置）
                    With ws.Range("E" & (pageStart) & ":N" & (pageStart + 5))
                        .MergeCells = True            ' 合并单元格
                        .Value = firstPageTitle       ' 设置标题文本
                    End With
                Else                                  ' 其他工作表
                    With ws.Range("G" & pageStart & ":L" & pageStart + 5)  ' 设置G-L列标题区域
                        .Font.Size = 14               ' 字体大小设14（其他页）
                        .Font.Bold = True             ' 设置为粗体
                    End With
                End If
                
                ' 边框设置 - 设置整个页面的边框样式
                With ws.Range("A" & pageStart & ":R" & pageEnd)  ' 整个页面区域
                    .Borders.LineStyle = xlNone               ' 先清除所有边框
                    .Borders(xlEdgeLeft).Weight = outerBorderWeight    ' 设置左外边框
                    .Borders(xlEdgeTop).Weight = outerBorderWeight     ' 设置上外边框
                    .Borders(xlEdgeBottom).Weight = outerBorderWeight  ' 设置下外边框
                    .Borders(xlEdgeRight).Weight = outerBorderWeight   ' 设置右外边框
                    .Borders(xlInsideHorizontal).Weight = innerBorderWeight  ' 设置内部水平线
                    .Borders(xlInsideVertical).Weight = innerBorderWeight    ' 设置内部垂直线
                End With
                
                 ' 表头增强边框 - 为表头区域设置更粗的边框（统一6行表头）
                With ws.Range("A" & pageStart & ":R" & (pageStart + 5))  ' 所有工作表统一6行表头
                    .Borders(xlEdgeLeft).Weight = headerBorderWeight      ' 设置左边框加粗
                    .Borders(xlEdgeTop).Weight = headerBorderWeight       ' 设置上边框加粗
                    .Borders(xlEdgeBottom).Weight = headerBorderWeight    ' 设置下边框加粗
                    .Borders(xlEdgeRight).Weight = headerBorderWeight     ' 设置右边框加粗
                End With
                
                
                ' 页码及工序处理 - 设置页码显示和工序编号
                If ws.Index > 1 Then                   ' 除首页外的所有页面
                    ' 全局页码 - 在页面右上角显示总页码
                    With ws.Range("Q" & pageStart & ":R" & (pageStart + 2))  ' 设置Q-R列前3行
                        .MergeCells = True                ' 合并单元格
                        .HorizontalAlignment = xlCenter   ' 水平居中
                        .VerticalAlignment = xlCenter     ' 垂直居中
                        .Value = "第" & currentGlobalPage & "页 共" & totalPages & "页"  ' 设置页码文本
                        .Font.Size = 10                   ' 字体大小10
                        .Font.Bold = False                 ' 默认非粗体
                    End With
                    
                    ' 工序处理 - 第二个工作表的工序编号
                    If ws.Index = 2 Then               ' 仅在第二个工作表执行
                        ' 构建工作表二标题内容
                        sheetTwoTitle = titlePrefix & "加工工艺指令" & vbCrLf & "Assembly Order"
                        
                        ' 设置工作表二标题
                        With ws.Range("G" & (pageStart) & ":L" & (pageStart + 5))
                            .MergeCells = True            ' 合并单元格
                            .Value = sheetTwoTitle        ' 设置标题文本
                        End With
                        
                        ' 清空A列10-45行的内容
                        ws.Range("A" & (pageStart + 9) & ":A" & (pageStart + 44)).ClearContents
                        
                        ' B列每两行合并为一个单元格并检查工序
                        For row = pageStart + 9 To pageEnd Step 2
                            ' 确保不会超出范围
                            If row + 1 <= pageEnd Then
                                ' 合并B列每两行
                                ws.Range("B" & row & ":B" & (row + 1)).Merge
                                
                                ' 获取当前工序名称
                                currentProcess = ws.Range("B" & row).Value
                                
                                ' 如果工序名称不为空，处理工序序号
                                If Not IsEmpty(currentProcess) And Trim(currentProcess) <> "" Then
                                    ' 判断是否为检验序
                                    isInspection = IsInspectionProcess(currentProcess)
                                    
                                    ' 合并A列对应行
                                    ws.Range("A" & row & ":A" & (row + 1)).Merge
                                    
                                    If isInspection Then
                                        ' 检验工序，A列填入"J"，不分配编号
                                        With ws.Range("A" & row)
                                            .Value = "J"
                                            .HorizontalAlignment = xlCenter
                                            .VerticalAlignment = xlCenter
                                            .Font.Size = 10
                                        End With
                                    Else
                                        ' 非检验工序，分配编号
                                        If Not processDict.Exists(currentProcess) Then
                                            processDict.Add currentProcess, processNumber  ' 添加到工序字典
                                            processNumber = processNumber + 10
                                        End If
                                        With ws.Range("A" & row)
                                            .Value = processDict(currentProcess)  ' 填入当前工序的编号
                                            .HorizontalAlignment = xlCenter
                                            .VerticalAlignment = xlCenter
                                            .Font.Size = 10
                                        End With
                                    End If
                                Else
                                    ' 如果B列为空，仍然合并A列但不填写序号
                                    ws.Range("A" & row & ":A" & (row + 1)).Merge
                                End If
                            End If
                        Next row
                    End If
                    
                    
                    ' 工序处理 - 第三个工作表的工序编号
                    If ws.Index = 3 Then               ' 仅在第三个工作表执行
                        ' 构建工作表二标题内容
                        sheetTwoTitle = titlePrefix & "加工工艺指令（附图页）" & vbCrLf & "Assembly Order"
                        
                        ' 设置工作表二标题
                        With ws.Range("G" & (pageStart) & ":L" & (pageStart + 5))
                            .MergeCells = True            ' 合并单元格
                            .Value = sheetTwoTitle        ' 设置标题文本
                        End With
                        
                    End If
                    
                     ' 工序处理 - 第四个工作表的工序编号
                    If ws.Index = 4 Then               ' 仅在第四个工作表执行
                        ' 构建工作表二标题内容
                        sheetTwoTitle = titlePrefix & "加工工艺流程卡" & vbCrLf & "Assembly Order"
                        
                        ' 设置工作表二标题
                        With ws.Range("G" & (pageStart) & ":L" & (pageStart + 5))
                            .MergeCells = True            ' 合并单元格
                            .Value = sheetTwoTitle        ' 设置标题文本
                        End With
                        
                    End If
                    
                End If
                
                ' 更新页码和行号
                currentGlobalPage = currentGlobalPage + 1  ' 全局页码增加
                pageStart = pageEnd + 1            ' 下一页的起始行为当前页的结束行+1
            Else
                Exit Do                            ' 如果没有数据则退出循环
            End If
        Loop  ' 结束主处理循环
    Next ws  ' 结束工作表遍历

    ' 统一字体设置 - 根据字符类型智能设置字体
    For Each ws In ThisWorkbook.Worksheets      ' 遍历所有工作表
        For Each cell In ws.UsedRange           ' 遍历每个工作表中的所有已使用单元格
            If Not IsEmpty(cell.Value) Then     ' 只处理非空单元格
                ws.Calculate                    ' 计算工作表公式
                If cell.HasFormula Then         ' 如果单元格包含公式
                    Dim displayText As String
                    displayText = cell.text     ' 获取公式计算后显示的文本
                    HasChinese = False          ' 初始化中文检测标志
                    ' 检查是否包含中文字符
                    For u = 1 To Len(displayText)
                        If IsChinese(Mid(displayText, u, 1)) Then
                            HasChinese = True   ' 如有中文字符则标记为True
                            Exit For            ' 找到中文后退出循环
                        End If
                    Next u
                    ' 根据是否包含中文选择字体
                    cell.Font.Name = IIf(HasChinese, "SimSun", "Times New Roman")
                Else                            ' 如果单元格不包含公式
                    cell.Font.Name = "Times New Roman"  ' 默认设为Times New Roman
                    ' 检查每个字符，对中文字符单独设置宋体
                    For u = 1 To Len(cell.Value)
                        If IsChinese(Mid(cell.Value, u, 1)) Then
                            cell.Characters(u, 1).Font.Name = "SimSun"  ' 中文字符设为宋体
                        End If
                    Next u
                End If
            End If
        Next cell
    Next ws

    ' 恢复Excel环境设置
    Application.ScreenUpdating = True               ' 恢复屏幕更新
    Application.Calculation = xlCalculationAutomatic ' 恢复自动计算
    MsgBox "格式设置完成！"                          ' 显示完成消息
End Sub  ' 结束主过程

' ▼▼▼▼▼ 文本格式化函数 ▼▼▼▼▼
' 功能：在单元格中查找指定文本并将其设为粗体和斜体，不改变其他格式
' 参数：cell - 要处理的单元格对象
'       searchText - 要查找并格式化的文本
Sub PartialBold(cell As Range, searchText As String)
    Dim startPos As Long                ' 匹配文本的起始位置
    Dim originalBold As Boolean         ' 原始粗体状态
    Dim originalItalic As Boolean       ' 原始斜体状态
    Dim compareMethod As VbCompareMethod ' 比较方法
    Dim i As Long                       ' 循环计数器
    Dim hasChineseInSegment As Boolean  ' 标记文本段是否包含中文
    
    ' 使用精确匹配（区分大小写和全半角）
    compareMethod = vbBinaryCompare     ' 设置为二进制比较（精确匹配）
    startPos = InStr(1, cell.Value, searchText, compareMethod)  ' 查找第一个匹配位置
    
    ' 循环查找所有匹配项
    Do While startPos > 0               ' 当找到匹配项时循环
        ' 保存原始格式状态
        originalBold = cell.Characters(startPos, Len(searchText)).Font.Bold
        originalItalic = cell.Characters(startPos, Len(searchText)).Font.Italic
        
        ' 检查文本段是否包含中文
        hasChineseInSegment = False
        For i = 1 To Len(searchText)
            If IsChinese(Mid(searchText, i, 1)) Then
                hasChineseInSegment = True
                Exit For
            End If
        Next i
        
        ' 仅当未设置格式时才设置（避免重复操作）
        If Not originalBold Or Not originalItalic Then  ' 如果文本未格式化
            With cell.Characters(startPos, Len(searchText))
                .Font.Bold = True       ' 设置为粗体
                .Font.Italic = True     ' 设置为斜体
                ' 根据是否含有中文选择合适的字体
                If hasChineseInSegment Then
                    .Font.Name = "SimSun"  ' 中文使用宋体
                Else
                    .Font.Name = cell.Font.Name  ' 保持原字体名称
                End If
                .Font.Size = cell.Font.Size  ' 保持原字体大小
            End With
        End If
        
        ' 继续查找下一个匹配
        startPos = InStr(startPos + Len(searchText), cell.Value, searchText, compareMethod)
    Loop
End Sub

' ▼▼▼▼▼ 首页对应复制 ▼▼▼▼▼
' 功能：将模板值复制到目标工作表的指定位置
' 参数：ws - 目标工作表
'       targetRow - 目标起始行号
'       targetCol - 目标起始列字母
'       templateValue - 要复制的模板值
Private Sub SyncTemplateValue(ws As Worksheet, targetRow As Long, targetCol As String, templateValue As String)
    On Error Resume Next               ' 忽略可能的错误（如单元格不存在或不是合并单元格）
    ' 直接写入目标区域左上角单元格
    With ws.Range(targetCol & targetRow).MergeArea.Cells(1, 1)  ' 获取合并区域的左上角单元格
        .Value = templateValue         ' 设置单元格值
        .HorizontalAlignment = xlCenter ' 水平居中
        .VerticalAlignment = xlCenter  ' 垂直居中
    End With
End Sub

' ▼▼▼▼▼ 智能字体 ▼▼▼▼▼
' 功能：根据单元格内容智能设置字体，中文使用宋体，非中文使用Times New Roman
' 参数：cell - 要设置字体的单元格对象
Private Sub SetCellFont(cell As Range)
    Dim displayText As String          ' 显示文本
    Dim HasChinese As Boolean          ' 是否包含中文标志
    Dim i As Long                      ' 循环计数器
    
    cell.Font.Name = "Times New Roman" ' 默认设置为Times New Roman
    
    If cell.HasFormula Then            ' 如果单元格包含公式
        displayText = cell.text        ' 获取显示文本（非公式本身）
        For i = 1 To Len(displayText)  ' 遍历每个字符
            If IsChinese(Mid(displayText, i, 1)) Then  ' 检查是否为中文
                cell.Font.Name = "SimSun" ' 如果包含中文，整个单元格设为宋体(使用英文字体名SimSun)
                Exit For               ' 找到中文后退出循环
            End If
        Next
    Else                               ' 如果单元格不包含公式
        For i = 1 To Len(cell.Value)   ' 遍历每个字符
            If IsChinese(Mid(cell.Value, i, 1)) Then  ' 检查是否为中文
                cell.Characters(i, 1).Font.Name = "SimSun"  ' 仅将中文字符设为宋体(使用英文字体名SimSun)
            End If
        Next
    End If
End Sub

' ▼▼▼▼▼ 中文检测函数 ▼▼▼▼▼
' 功能：检测指定字符是否为中文字符（Unicode范围）
' 参数：char - 要检测的单个字符
' 返回：Boolean - 如果是中文返回True，否则返回False
Function IsChinese(ByVal char As String) As Boolean
    Dim code As Long                   ' 字符的Unicode编码
    code = AscW(char)                  ' 获取字符的Unicode编码
    If code < 0 Then code = code + 65536  ' 处理负值编码（扩展Unicode）
    
    ' 检查是否在中文Unicode范围内
    ' CJK统一汉字范围(4E00-9FFF)
    ' CJK统一汉字扩展A(3400-4DBF)
    ' CJK统一汉字扩展B(20000-2A6DF)
    ' CJK兼容汉字(F900-FAFF)
    IsChinese = (code >= &H4E00 And code <= &H9FFF) Or _
                (code >= &H3400 And code <= &H4DBF) Or _
                (code >= &HF900 And code <= &HFAFF) Or _
                (code >= &H20000 And code <= &H2A6DF)
End Function

' ▼▼▼▼▼ 清除已有编号格式 ▼▼▼▼▼
' 功能：从字符串中移除开头的工序编号格式（形如"10-1."）
' 参数：str - 要处理的字符串
' 返回：String - 移除编号后的字符串
Function RemoveExistingNumber(str As String) As String
    Dim regex As Object                ' 正则表达式对象
    Set regex = CreateObject("VBScript.RegExp")  ' 创建正则表达式对象
    ' 匹配"数字-数字."格式（如"10-1."）
    With regex
        .Pattern = "^\d+-\d+\."        ' 设置正则表达式模式
        .Global = True                 ' 全局匹配
        .IgnoreCase = True             ' 忽略大小写
    End With
    ' 替换符合模式的内容为空字符串
    RemoveExistingNumber = regex.Replace(str, "")  ' 返回替换后的结果
End Function

' ▼▼▼▼▼ 中文提取函数 ▼▼▼▼▼
' 功能：从字符串中提取第一个中文字符开始到结尾的部分
' 参数：str - 要处理的字符串
' 返回：String - 提取的中文部分，如果没有中文则返回原字符串
Function GetChinesePart(str As String) As String
    Dim i As Long                      ' 循环计数器
    For i = 1 To Len(str)              ' 遍历字符串的每个字符
        If IsChinese(Mid(str, i, 1)) Then  ' 检查当前字符是否为中文
            GetChinesePart = Mid(str, i)   ' 提取从当前位置到结尾的子字符串
            Exit Function                   ' 找到中文后立即返回
        End If
    Next i
    GetChinesePart = str               ' 如果没有找到中文，返回原字符串
End Function

' ▼▼▼▼▼ 工作表页数计算 ▼▼▼▼▼
' 功能：计算指定工作表包含的页数（根据pageSize定义的每页行数）
' 参数：ws - 要计算的工作表
'       pageSize - 每页的行数
' 返回：Long - 工作表的页数
Private Function GetSheetPageCount(ws As Worksheet, pageSize As Long) As Long
    Dim pageStart As Long              ' 页面起始行
    Dim pageEnd As Long                ' 页面结束行
    Dim hasData As Boolean             ' 是否有数据标志
    Dim pageCount As Long              ' 页数计数器
    Dim dataCheck As Range             ' 数据检查范围
    
    pageStart = 1                      ' 初始化起始行为1
    hasData = True                     ' 初始化有数据标志为True
    pageCount = 0                      ' 初始化页数计数为0
    
    Do While hasData                   ' 循环直到没有数据
        pageEnd = pageStart + pageSize - 1  ' 计算当前页的结束行
        If pageEnd > ws.Rows.count Then Exit Do  ' 如果超出工作表最大行数则退出循环
        
        Set dataCheck = ws.Range("A" & pageStart & ":R" & pageEnd)  ' 设置数据检查范围
        hasData = (Application.CountA(dataCheck) > 0)  ' 检查范围内是否有数据
        
        If hasData Then                ' 如果有数据
            pageCount = pageCount + 1  ' 页数计数增加
            pageStart = pageEnd + 1    ' 更新下一页的起始行
        Else
            Exit Do                    ' 如果没有数据则退出循环
        End If
    Loop
    
    GetSheetPageCount = pageCount      ' 返回计算的页数
End Function

' ▼▼▼▼▼ 识别新表头区域并统计数量 ▼▼▼▼▼
' 功能：识别以"指令编号"换行"Order No."为标记的新表头区域，并统计工作表二中的数量
' 参数：无
' 返回：Long - 工作表二中新表头区域的数量
Function CountNewHeaderRegions() As Long
    Dim ws As Worksheet               ' 工作表对象
    Dim cell As Range                 ' 单元格对象
    Dim count As Long                 ' 计数器
    Dim i As Long, j As Long          ' 循环变量
    Dim headerText As String          ' 单元格文本
    
    ' 初始化计数器
    count = 0
    
    ' 获取工作表二
    On Error Resume Next
    Set ws = ThisWorkbook.Worksheets(2)
    On Error GoTo 0
    
    ' 确保工作表二存在
    If ws Is Nothing Then
        MsgBox "工作表二不存在！", vbExclamation
        CountNewHeaderRegions = 0
        Exit Function
    End If
    
    ' 在工作表二中查找所有包含"指令编号"换行"Order No."的单元格
    For i = 1 To ws.UsedRange.Rows.count
        For j = 1 To ws.UsedRange.Columns.count
            Set cell = ws.Cells(i, j)
            
            ' 跳过空单元格
            If Not IsEmpty(cell.Value) Then
                ' 获取单元格文本并检查是否包含目标文本
                headerText = CStr(cell.Value)
                
                ' 检查是否包含"指令编号"和"Order No."（可能以换行符分隔）
                If InStr(headerText, "指令编号") > 0 And InStr(headerText, "Order No.") > 0 Then
                    ' 检查该单元格是否为合并单元格
                    If cell.MergeCells Then
                        ' 检查合并单元格是否为3行高
                        If cell.MergeArea.Rows.count = 3 Then
                            count = count + 1
                            
                            ' 可以在这里添加处理新表头区域的代码
                            ' 例如：设置特定格式或其他操作
                            ' 新表头区域为 newHeaderStart 到 newHeaderStart + 8
                        End If
                    End If
                End If
            End If
        Next j
    Next i
    
    ' 返回找到的新表头区域数量
    CountNewHeaderRegions = count
End Function

' ▼▼▼▼▼ 平衡数据行数量 ▼▼▼▼▼
' 功能：确保每个数据组尽量保持18个数据行，多余的移到下一组，不足的从下一组补充
' 参数：无
Sub BalanceDataRows()
    ' ===========================================================================
    ' 模块: 平衡数据行工具
    ' 版本: 3.1
    ' 日期: 2023-12-06
    ' 作者: 开发团队
    ' 描述: 根据流程图逻辑，重构数据行平衡算法，移除最少保留1行的限制
    ' ===========================================================================
    
    Dim ws As Worksheet               ' 工作表对象
    Dim cell As Range                 ' 单元格对象
    Dim i As Long, j As Long          ' 循环变量
    Dim headerStarts() As Long        ' 存储所有新表头起始行
    Dim headerCount As Long           ' 新表头计数
    Dim headerText As String          ' 单元格文本
    Dim targetRowCount As Long        ' 每组目标数据行数
    Dim rowsNeeded As Long            ' 需要移动的行数
    Dim rowsToMove As Long            ' 需要移动的行数
    Dim currentGroupEnd As Long       ' 当前组末尾位置
    Dim nextGroupStart As Long        ' 下一组起始位置
    Dim excessRowStart As Long        ' 多余数据行起始位置
    Dim excessRowEnd As Long          ' 多余数据行结束位置
    Dim operationCount As Long        ' 操作计数
    Dim originalDisplayAlerts As Boolean ' 保存原始警告提示设置
    Dim emptyRowsRemoved As Long      ' 删除的空白行数量
    Dim newHeaderRowCount As Long     ' 新表头行数
    Dim hasNextHeader As Boolean      ' 是否有下一个表头
    
    ' 保存原始警告提示设置
    originalDisplayAlerts = Application.DisplayAlerts
    
    ' 关闭Excel的警告提示
    Application.DisplayAlerts = False
    
    ' 设置每组目标数据行数
    targetRowCount = 18
    
    ' 初始化计数器和数组
    headerCount = 0
    ReDim headerStarts(1 To 100)      ' 假设最多100个表头
    newHeaderRowCount = 9             ' 新表头固定占9行
    
    ' 关闭屏幕更新以提高性能
    Application.ScreenUpdating = False
    
    ' 先运行空白行检测删除函数
    emptyRowsRemoved = DetectAndRemoveEmptyRows()
    If emptyRowsRemoved > 0 Then
        MsgBox "已删除 " & emptyRowsRemoved & " 个空白数据行。", vbInformation
    End If
    
    ' 获取工作表二
    On Error Resume Next
    Set ws = ThisWorkbook.Worksheets(2)
    On Error GoTo 0
    
    ' 确保工作表二存在
    If ws Is Nothing Then
        MsgBox "工作表二不存在！", vbExclamation
        ' 恢复Excel的警告提示和屏幕更新
        Application.DisplayAlerts = originalDisplayAlerts
        Application.ScreenUpdating = True
        Exit Sub
    End If
    
    ' 首先识别所有新表头位置
    For i = 1 To ws.UsedRange.Rows.count
        For j = 1 To ws.UsedRange.Columns.count
            Set cell = ws.Cells(i, j)
            
            ' 跳过空单元格
            If Not IsEmpty(cell.Value) Then
                ' 获取单元格文本并检查是否包含目标文本
                headerText = CStr(cell.Value)
                
                ' 检查是否包含"指令编号"和"Order No."
                If InStr(headerText, "指令编号") > 0 And InStr(headerText, "Order No.") > 0 Then
                    ' 检查该单元格是否为合并单元格
                    If cell.MergeCells Then
                        ' 检查合并单元格是否为3行高
                        If cell.MergeArea.Rows.count = 3 Then
                            headerCount = headerCount + 1
                            headerStarts(headerCount) = i
                            Debug.Print "找到新表头 #" & headerCount & " 在行: " & i
                        End If
                    End If
                End If
            End If
        Next j
    Next i
    
    ' 确保至少有一个表头
    If headerCount < 1 Then
        MsgBox "未找到任何表头！", vbExclamation
        Application.ScreenUpdating = True
        Exit Sub
    End If
    
    ' 定义数组存储每个数据组的起始行、结束行和数据行数量
    Dim dataGroupStarts() As Long    ' 每个数据组的起始行
    Dim dataGroupEnds() As Long      ' 每个数据组的结束行
    Dim dataRowCounts() As Long      ' 每个数据组的数据行数量
    
    ReDim dataGroupStarts(1 To headerCount)
    ReDim dataGroupEnds(1 To headerCount)
    ReDim dataRowCounts(1 To headerCount)
    
    ' 计算每个数据组的起始行、结束行和数据行数量
    For i = 1 To headerCount
        ' 数据组起始行是表头后第9行
        dataGroupStarts(i) = headerStarts(i) + 9
        
        ' 数据组结束行是下一个表头前一行，如果是最后一个表头则到工作表末尾
        If i < headerCount Then
            dataGroupEnds(i) = headerStarts(i + 1) - 1
        Else
            ' 最后一个表头的数据区域到工作表的已用区域末尾行
            dataGroupEnds(i) = ws.UsedRange.Rows.count
        End If
        
        ' 计算每组的数据行数（每两行为一个数据行）
        dataRowCounts(i) = Int((dataGroupEnds(i) - dataGroupStarts(i) + 1) / 2)
        
        Debug.Print "数据组 #" & i & ": 起始行 " & dataGroupStarts(i) & "，结束行 " & dataGroupEnds(i) & "，数据行数 " & dataRowCounts(i)
    Next i
    
    ' 从第一个数据组开始处理
    operationCount = 0
    
    ' 按照流程图逻辑处理每个数据组
    For i = 1 To headerCount
        ' 首先判断数据组行数是否等于目标行数
        If dataRowCounts(i) = targetRowCount Then
            Debug.Print "数据组 #" & i & " 已有 " & targetRowCount & " 行，无需调整"
            ' 跳过当前迭代，继续下一个数据组
            GoTo NextGroup
        End If
        
        ' 检查是否有下一个表头
        hasNextHeader = (i < headerCount)
        
        ' 判断数据组行数是否小于目标行数
        If dataRowCounts(i) < targetRowCount Then
            ' 如果有下一个表头，则判断是否从下一组移入数据
            If hasNextHeader Then
                ' 存在下一组且有数据
                If dataRowCounts(i + 1) > 0 Then
                    ' 下一组有数据，计算需要移动的行数
                    rowsNeeded = targetRowCount - dataRowCounts(i)
                    
                    ' 不再限制下一组必须保留1行，可以全部移动
                    If dataRowCounts(i + 1) < rowsNeeded Then
                        rowsNeeded = dataRowCounts(i + 1)  ' 移动下一组所有可用行
                    End If
                    
                    ' 如果需要移动的行数小于等于0，则跳过
                    If rowsNeeded <= 0 Then
                        GoTo NextGroup
                    End If
                    
                    Debug.Print "数据组 #" & i & " 有 " & dataRowCounts(i) & " 行，需要从下一组移动 " & rowsNeeded & " 行"
                    
                    ' 保存当前组末尾位置和下一组开始位置
                    currentGroupEnd = dataGroupEnds(i) + 1
                    nextGroupStart = dataGroupStarts(i + 1)
                    
                    ' 移动数据行块（保持格式和内容完整性）
                    MoveDataRowBlock ws, nextGroupStart, nextGroupStart + (rowsNeeded * 2) - 1, currentGroupEnd
                    
                    ' 更新数据组边界
                    dataGroupEnds(i) = dataGroupEnds(i) + (rowsNeeded * 2)
                    dataGroupStarts(i + 1) = dataGroupStarts(i + 1) + (rowsNeeded * 2)
                    
                    ' 更新数据行计数
                    dataRowCounts(i) = dataRowCounts(i) + rowsNeeded
                    dataRowCounts(i + 1) = dataRowCounts(i + 1) - rowsNeeded
                    
                    operationCount = operationCount + 1
                Else
                    ' 下一组表头存在但没有数据，按流程图逻辑应删除下一组表头
                    ' 删除下一组表头
                    ws.Rows(headerStarts(i + 1) & ":" & (headerStarts(i + 1) + newHeaderRowCount - 1)).Delete Shift:=xlUp
                    
                    ' 更新后续表头的位置信息
                    For j = i + 1 To headerCount - 1
                        headerStarts(j) = headerStarts(j + 1) - newHeaderRowCount
                        dataGroupStarts(j) = dataGroupStarts(j + 1) - newHeaderRowCount
                        dataGroupEnds(j) = dataGroupEnds(j + 1) - newHeaderRowCount
                        dataRowCounts(j) = dataRowCounts(j + 1)
                    Next j
                    
                    ' 减少表头计数
                    headerCount = headerCount - 1
                    ReDim Preserve headerStarts(1 To headerCount)
                    ReDim Preserve dataGroupStarts(1 To headerCount)
                    ReDim Preserve dataGroupEnds(1 To headerCount)
                    ReDim Preserve dataRowCounts(1 To headerCount)
                    
                    ' 更新当前组的结束行（可能延伸到了原下一组的区域）
                    If i < headerCount Then
                        dataGroupEnds(i) = headerStarts(i + 1) - 1
                    Else
                        dataGroupEnds(i) = ws.UsedRange.Rows.count
                    End If
                    
                    ' 重新计算当前组的数据行数
                    dataRowCounts(i) = Int((dataGroupEnds(i) - dataGroupStarts(i) + 1) / 2)
                    
                    ' 检查更新后的行数是否符合要求
                    If dataRowCounts(i) < targetRowCount Then
                        ' 仍然不足，创建空白行补足
                        rowsNeeded = targetRowCount - dataRowCounts(i)
                        
                        If dataRowCounts(i) > 0 Then
                            ' 使用当前组最后一个数据行作为模板
                            Dim templateRowStart As Long, templateRowEnd As Long
                            templateRowStart = dataGroupStarts(i)
                            templateRowEnd = templateRowStart + 1
                            
                            ' 计算当前组的最后一行
                            Dim currentLastRow As Long
                            currentLastRow = dataGroupStarts(i) + (dataRowCounts(i) * 2)
                            
                            ' 添加空白行补足
                            For j = 1 To rowsNeeded
                                CopyEmptyRow ws, templateRowStart, templateRowEnd, currentLastRow
                                currentLastRow = currentLastRow + 2
                                operationCount = operationCount + 1
                            Next j
                            
                            ' 更新数据行计数
                            dataRowCounts(i) = targetRowCount
                        End If
                    End If
                    
                    operationCount = operationCount + 1
                End If
            Else
                ' 没有下一个表头，创建空白行补足
                rowsNeeded = targetRowCount - dataRowCounts(i)
                
                ' 如果当前组有数据行，使用第一行作为模板
                If dataRowCounts(i) > 0 Then
                    templateRowStart = dataGroupStarts(i)
                    templateRowEnd = templateRowStart + 1
                    currentLastRow = dataGroupStarts(i) + (dataRowCounts(i) * 2)
                    
                    ' 添加空白行补足
                    For j = 1 To rowsNeeded
                        CopyEmptyRow ws, templateRowStart, templateRowEnd, currentLastRow
                        currentLastRow = currentLastRow + 2
                        operationCount = operationCount + 1
                    Next j
                    
                    ' 更新数据行计数
                    dataRowCounts(i) = targetRowCount
                Else
                    ' 当前组没有数据行，创建新的空白数据行
                    For j = 1 To targetRowCount
                        CreateEmptyRow ws, dataGroupStarts(i) + ((j - 1) * 2)
                        operationCount = operationCount + 1
                    Next j
                    
                    ' 更新数据行计数
                    dataRowCounts(i) = targetRowCount
                End If
            End If
        Else
            ' 数据组行数大于目标行数，处理多余行
            ' 判断是否有下一个表头
            If hasNextHeader Then
                ' 有下一个表头，移动多余行到下一组
                rowsToMove = dataRowCounts(i) - targetRowCount
                
                Debug.Print "数据组 #" & i & " 有 " & dataRowCounts(i) & " 行，需要移动 " & rowsToMove & " 行到下一组"
                
                ' 计算多余数据行的起始和结束位置
                excessRowStart = dataGroupEnds(i) - (rowsToMove * 2) + 1
                excessRowEnd = dataGroupEnds(i)
                
                ' 下一组的数据起始行
                nextGroupStart = dataGroupStarts(i + 1)
                
                ' 移动数据行块（保持格式和内容完整性）
                MoveDataRowBlock ws, excessRowStart, excessRowEnd, nextGroupStart
                
                ' 更新数据组边界
                dataGroupEnds(i) = dataGroupEnds(i) - (rowsToMove * 2)
                dataGroupStarts(i + 1) = dataGroupStarts(i + 1) - (rowsToMove * 2)
                
                ' 更新数据行计数
                dataRowCounts(i) = dataRowCounts(i) - rowsToMove
                dataRowCounts(i + 1) = dataRowCounts(i + 1) + rowsToMove
                
                operationCount = operationCount + 1
            Else
                ' 没有下一个表头，需要创建新表头
                rowsToMove = dataRowCounts(i) - targetRowCount
                
                Debug.Print "数据组 #" & i & " 有 " & dataRowCounts(i) & " 行，需要创建新表头处理多余的 " & rowsToMove & " 行"
                
                ' 计算新表头插入位置
                Dim newHeaderInsertRow As Long
                newHeaderInsertRow = dataGroupStarts(i) + (targetRowCount * 2)
                
                ' 计算第一个表头位置（用于复制）
                Dim firstHeaderStart As Long, firstHeaderEnd As Long
                firstHeaderStart = headerStarts(1)
                firstHeaderEnd = firstHeaderStart + newHeaderRowCount - 1
                
                ' 在工作表末尾创建新表头空白行
                Dim wsLastRow As Long, blankHeaderRow As Long
                wsLastRow = ws.UsedRange.Rows.count
                blankHeaderRow = wsLastRow + 1
                
                ws.Rows(blankHeaderRow & ":" & (blankHeaderRow + newHeaderRowCount - 1)).Insert Shift:=xlDown
                
                ' 复制第一个表头的内容和格式
                ws.Range("A" & firstHeaderStart & ":R" & firstHeaderEnd).Copy
                ws.Range("A" & blankHeaderRow).PasteSpecial xlPasteAll
                Application.CutCopyMode = False
                
                ' 移动新表头到目标位置
                MoveDataRowBlock ws, blankHeaderRow, blankHeaderRow + newHeaderRowCount - 1, newHeaderInsertRow
                
                ' 更新表头数组
                headerCount = headerCount + 1
                ReDim Preserve headerStarts(1 To headerCount)
                headerStarts(headerCount) = newHeaderInsertRow
                
                ' 计算新的数据组起始行
                Dim newGroupStart As Long
                newGroupStart = newHeaderInsertRow + newHeaderRowCount
                
                ' 添加新数据组信息
                ReDim Preserve dataGroupStarts(1 To headerCount)
                ReDim Preserve dataGroupEnds(1 To headerCount)
                ReDim Preserve dataRowCounts(1 To headerCount)
                
                ' 设置新数据组边界
                dataGroupStarts(headerCount) = newGroupStart
                dataGroupEnds(headerCount) = ws.UsedRange.Rows.count
                
                ' 计算新数据组的行数
                dataRowCounts(headerCount) = rowsToMove
                
                ' 更新原数据组行数
                dataRowCounts(i) = targetRowCount
                
                ' 如果新数据组行数小于targetRowCount，补足行数
                If rowsToMove < targetRowCount Then
                    ' 使用新数据组第一行作为模板
                    templateRowStart = newGroupStart
                    templateRowEnd = newGroupStart + 1
                    currentLastRow = newGroupStart + (rowsToMove * 2)
                    
                    ' 计算需要补充的行数
                    rowsNeeded = targetRowCount - rowsToMove
                    
                    ' 添加空白行补足
                    For j = 1 To rowsNeeded
                        CopyEmptyRow ws, templateRowStart, templateRowEnd, currentLastRow
                        currentLastRow = currentLastRow + 2
                    Next j
                    
                    ' 更新数据行计数
                    dataRowCounts(headerCount) = targetRowCount
                End If
                
                operationCount = operationCount + 1
            End If
        End If
NextGroup:
    Next i
    
    ' 恢复屏幕更新和Excel警告提示
    Application.ScreenUpdating = True
    Application.DisplayAlerts = originalDisplayAlerts
    
    ' 显示操作结果
    If operationCount > 0 Then
        MsgBox "平衡操作完成！共执行了 " & operationCount & " 次操作。", vbInformation
    Else
        MsgBox "所有数据组已经平衡，无需调整。", vbInformation
    End If
    
    ' 调整分页预览线框
    AdjustPageBreaks
End Sub

' ▼▼▼▼▼ 处理最后一个数据组 ▼▼▼▼▼
' 功能：处理末尾数据组，确保其数据行数量为targetRowCount
' 参数：ws - 工作表对象
'       headerStarts - 所有新表头的起始行数组
'       headerCount - 当前新表头数量
'       targetRowCount - 目标数据行数量
'       lastGroupStart - 最后一个数据组的起始行
'       lastGroupRowCount - 最后一个数据组的当前数据行数量
' 返回：Long - 执行的操作次数
Function ProcessLastGroup(ws As Worksheet, headerStarts() As Long, headerCount As Long, targetRowCount As Long, _
                         lastGroupStart As Long, lastGroupRowCount As Long) As Long
    
    Dim operationCount As Long
    Dim lastGroupEnd As Long
    Dim rowsToAdd As Long
    Dim prevGroupEnd As Long
    Dim templateStart As Long, templateEnd As Long
    Dim templateRowStart As Long, templateRowEnd As Long
    Dim currentLastRow As Long
    Dim lastHeaderStart As Long, lastHeaderEnd As Long
    Dim newHeaderInsertRow As Long
    Dim newLastGroupStart As Long
    Dim excessRows As Long
    Dim moveRowStart As Long, moveRowEnd As Long
    Dim i As Long
    Dim headerRowCount As Long        ' 新表头的实际行数（表格行）
    Dim insertedRow As Long
    Dim firstHeaderStart As Long      ' 第一个新表头的起始行
    Dim firstHeaderEnd As Long        ' 第一个新表头的结束行
    Dim wsLastRow As Long             ' 工作表的最后一行
    Dim blankHeaderRow As Long        ' 新创建的新表头空白行的起始位置
    
    operationCount = 0
    
    ' 如果尚未计算最后一个数据组的数据行数量，则先计算
    If lastGroupRowCount = 0 Then
        ' 计算到工作表末尾的行数
        lastGroupEnd = ws.UsedRange.Rows.count
        
        ' 确保末尾数据行不超出工作表范围
        If lastGroupEnd < lastGroupStart Then
            lastGroupEnd = lastGroupStart
        End If
        
        ' 计算数据行数量（每两行为一个数据行）
        lastGroupRowCount = Int((lastGroupEnd - lastGroupStart + 1) / 2)
    End If
    
    Debug.Print "处理最后一个数据组（新表头 #" & headerCount & "）: 起始行 " & lastGroupStart & "，数据行数 " & lastGroupRowCount
    
    ' 判定1: 如果数据行数量正好等于targetRowCount，不进行操作
    If lastGroupRowCount = targetRowCount Then
        Debug.Print "末尾数据组数量为 " & targetRowCount & " 行，无需调整"
        ProcessLastGroup = 0
        Exit Function
    End If
    
    ' 判定2: 如果数据行数量小于targetRowCount，复制已有数据行补足
    If lastGroupRowCount < targetRowCount Then
        Debug.Print "末尾数据组数量为 " & lastGroupRowCount & " 行，需要补足至 " & targetRowCount & " 行"
        
        ' 计算需要补充的行数
        rowsToAdd = targetRowCount - lastGroupRowCount
        
        ' 如果末尾组本身没有数据行，则至少需要有一个模板行可复制
        If lastGroupRowCount = 0 Then
            ' 尝试从前一个数据组复制一个数据行作为模板
            If headerCount > 1 Then
                ' 计算前一个数据组的最后一个数据行
                prevGroupEnd = lastGroupStart - 1  ' 当前新表头前一行
                
                ' 前一个新表头的最后一个数据行
                templateStart = prevGroupEnd - 1
                templateEnd = prevGroupEnd
                
                ' 复制模板行到末尾数据组起始位置
                CopyEmptyRow ws, templateStart, templateEnd, lastGroupStart
                lastGroupRowCount = 1
                rowsToAdd = rowsToAdd - 1
                operationCount = operationCount + 1
            Else
                ' 如果没有前一个数据组，则创建一个新的空白数据行
                CreateEmptyRow ws, lastGroupStart
                lastGroupRowCount = 1
                rowsToAdd = rowsToAdd - 1
                operationCount = operationCount + 1
            End If
        End If
        
        ' 现在复制末尾组中的第一个数据行作为模板
        templateRowStart = lastGroupStart
        templateRowEnd = lastGroupStart + 1
        
        ' 计算当前末尾数据组的最后一行
        currentLastRow = lastGroupStart + (lastGroupRowCount * 2)
        
        ' 添加所需的空白数据行
        For i = 1 To rowsToAdd
            CopyEmptyRow ws, templateRowStart, templateRowEnd, currentLastRow
            currentLastRow = currentLastRow + 2
            operationCount = operationCount + 1
        Next i
        
        ' 返回执行的操作次数
        ProcessLastGroup = operationCount
        Exit Function
    End If
    
    ' 判定3: 如果数据行数量大于targetRowCount，创建新的"新表头"并移动多余数据行
    If lastGroupRowCount > targetRowCount Then
        Debug.Print "末尾数据组数量为 " & lastGroupRowCount & " 行，需要创建新的新表头处理多余的 " & (lastGroupRowCount - targetRowCount) & " 行"
        
        ' 关闭Excel的警告提示
        Application.DisplayAlerts = False
        
        ' 新表头固定占9行
        headerRowCount = 9
        
        ' 计算第一个新表头的位置（用于复制）
        firstHeaderStart = headerStarts(1)
        firstHeaderEnd = firstHeaderStart + headerRowCount - 1
        
        ' 计算新的"新表头"插入位置：在当前末尾数据组targetRowCount个数据行之后
        newHeaderInsertRow = lastGroupStart + (targetRowCount * 2)
        
        ' ===== 步骤1：在末尾创建新的"新表头"空白行 =====
        
        ' 获取工作表的最后一行
        wsLastRow = ws.UsedRange.Rows.count
        
        ' 在工作表末尾创建9行空白行（用于新的"新表头"）
        blankHeaderRow = wsLastRow + 1
        ws.Rows(blankHeaderRow & ":" & (blankHeaderRow + headerRowCount - 1)).Insert Shift:=xlDown
        
        Debug.Print "在工作表末尾创建空白新表头行: 行 " & blankHeaderRow & " 到 " & (blankHeaderRow + headerRowCount - 1)
        
        ' ===== 步骤2：复制第一个新表头到空白行 =====
        
        ' 复制第一个新表头的内容和格式
        ws.Range("A" & firstHeaderStart & ":R" & firstHeaderEnd).Copy
        
        ' 粘贴到新创建的空白行
        ws.Range("A" & blankHeaderRow).PasteSpecial xlPasteAll
        
        ' 清除剪贴板
        Application.CutCopyMode = False
        
        Debug.Print "复制第一个新表头到空白行: 从行 " & firstHeaderStart & "-" & firstHeaderEnd & " 到行 " & blankHeaderRow
        
        ' ===== 步骤3：移动新创建的"新表头"到目标位置 =====
        
        ' 移动新创建的"新表头"到目标位置
        MoveDataRowBlock ws, blankHeaderRow, blankHeaderRow + headerRowCount - 1, newHeaderInsertRow
        
        Debug.Print "移动新创建的新表头到目标位置: 从行 " & blankHeaderRow & " 到行 " & newHeaderInsertRow
        
        ' 更新新表头数组和计数
        headerCount = headerCount + 1
        ReDim Preserve headerStarts(1 To headerCount)
        headerStarts(headerCount) = newHeaderInsertRow
        
        ' 计算新的末尾数据组起始行
        newLastGroupStart = newHeaderInsertRow + headerRowCount
        
        ' ===== 步骤4：移动多余的数据行到新创建的"新表头"下方 =====
        
        ' 计算需要移动的行数
        excessRows = lastGroupRowCount - targetRowCount
        
        ' 计算原末尾组中多余数据行的起始和结束位置
        ' 修正：考虑新表头插入后的位置变化
        ' 原来的计算方式：
        ' moveRowStart = lastGroupStart + (targetRowCount * 2)
        ' moveRowEnd = lastGroupStart + (lastGroupRowCount * 2) - 1
        
        ' 新的计算方式：
        ' 由于新表头已经插入，原来的多余数据行现在位置应该是新表头后面
        moveRowStart = newHeaderInsertRow + headerRowCount
        moveRowEnd = moveRowStart + (excessRows * 2) - 1
        
        ' 移动数据行块到新末尾组（这步实际上不需要移动，因为数据行已经在正确位置）
        ' 我们只需要处理一下格式和内容，确保它们符合要求
        
        ' 检查是否有更多数据行需要处理（如果超过18行）
        If excessRows > targetRowCount Then
            ' 如果溢出数据行数量大于目标行数，需要递归处理
            ' 先处理当前新表头下的数据行，确保不超过targetRowCount
            
            Debug.Print "新数据组溢出数量为 " & excessRows & " 行，大于目标 " & targetRowCount & " 行，需要递归处理"
            
            ' 递归调用ProcessLastGroup处理新的末尾数据组
            Dim recursiveOperations As Long
            recursiveOperations = ProcessLastGroup(ws, headerStarts, headerCount, targetRowCount, newLastGroupStart, excessRows)
            operationCount = operationCount + recursiveOperations
        Else
            ' 如果溢出数据行数量小于目标行数，需要补足
            If excessRows < targetRowCount Then
                Debug.Print "新数据组数量为 " & excessRows & " 行，需要补足至 " & targetRowCount & " 行"
                
                ' 计算需要补充的行数
                rowsToAdd = targetRowCount - excessRows
                
                ' 使用第一个数据行作为模板
                templateRowStart = newLastGroupStart
                templateRowEnd = newLastGroupStart + 1
                
                ' 计算当前末尾数据组的最后一行
                currentLastRow = newLastGroupStart + (excessRows * 2)
                
                ' 添加所需的空白数据行
                For i = 1 To rowsToAdd
                    CopyEmptyRow ws, templateRowStart, templateRowEnd, currentLastRow
                    currentLastRow = currentLastRow + 2
                    operationCount = operationCount + 1
                Next i
            End If
        End If
        
        ' 恢复Excel的警告提示
        Application.DisplayAlerts = True
        
        operationCount = operationCount + 1
        
        ' 返回总操作次数
        ProcessLastGroup = operationCount
        Exit Function
    End If
    
    ' 默认返回0
    ProcessLastGroup = 0
End Function

' ▼▼▼▼▼ 移动数据行块辅助函数 ▼▼▼▼▼
' 功能：移动一整块数据行，保持格式、内容和相对顺序
' 参数：ws - 工作表对象
'       sourceStart - 源数据块起始行
'       sourceEnd - 源数据块结束行
'       targetRow - 目标位置行
Private Sub MoveDataRowBlock(ws As Worksheet, sourceStart As Long, sourceEnd As Long, targetRow As Long)
    ' 计算要移动的行数
    Dim rowCount As Long
    rowCount = sourceEnd - sourceStart + 1
    
    ' 关闭Excel的警告提示
    Application.DisplayAlerts = False
    
    ' 选择源数据块
    Dim sourceRange As Range
    Set sourceRange = ws.Range("A" & sourceStart & ":R" & sourceEnd)
    
    ' 复制源数据块（包括内容和格式）
    sourceRange.Copy
    
    ' 在目标位置插入足够的空白行
    ws.Rows(targetRow & ":" & (targetRow + rowCount - 1)).Insert Shift:=xlDown
    
    ' 粘贴到目标位置
    ws.Range("A" & targetRow).PasteSpecial xlPasteAll
    
    ' 清除剪贴板
    Application.CutCopyMode = False
    
    ' 删除源数据块
    sourceRange.EntireRow.Delete Shift:=xlUp
    
    ' 恢复Excel的警告提示
    Application.DisplayAlerts = True
    
    Debug.Print "移动数据块: 从行 " & sourceStart & "-" & sourceEnd & " 到行 " & targetRow & "，共 " & rowCount & " 行"
End Sub

' ▼▼▼▼▼ 复制表头 ▼▼▼▼▼
' 功能：复制表头到新位置
' 参数：ws - 工作表对象
'       sourceStart - 源表头起始行
'       sourceEnd - 源表头结束行
'       targetRow - 目标位置行
Private Sub CopyHeader(ws As Worksheet, sourceStart As Long, sourceEnd As Long, targetRow As Long)
    ' 关闭Excel的警告提示
    Application.DisplayAlerts = False
    
    ' 计算要复制的行数（表格行）
    Dim rowCount As Long
    rowCount = sourceEnd - sourceStart + 1
    
    ' 确保我们复制的是完整的表头（应该是9行）
    If rowCount <> 9 Then
        Debug.Print "警告：表头行数不是9行，实际行数为 " & rowCount & " 行"
    End If
    
    ' 1. 在目标位置插入足够的空白行
    ws.Rows(targetRow & ":" & (targetRow + rowCount - 1)).Insert Shift:=xlDown
    
    ' 2. 复制源表头内容和格式
    ws.Range("A" & sourceStart & ":R" & sourceEnd).Copy
    
    ' 3. 粘贴到新插入的空白行
    ws.Range("A" & targetRow).PasteSpecial xlPasteAll
    
    ' 清除剪贴板
    Application.CutCopyMode = False
    
    ' 恢复Excel的警告提示
    Application.DisplayAlerts = True
    
    Debug.Print "复制表头: 从行 " & sourceStart & "-" & sourceEnd & " 到行 " & targetRow & "，共 " & rowCount & " 行"
End Sub

' ▼▼▼▼▼ 复制数据行并清空内容 ▼▼▼▼▼
' 功能：复制数据行到新位置，保留格式但清空内容
' 参数：ws - 工作表对象
'       sourceStart - 源数据行起始行
'       sourceEnd - 源数据行结束行
'       targetRow - 目标位置行
Private Sub CopyEmptyRow(ws As Worksheet, sourceStart As Long, sourceEnd As Long, targetRow As Long)
    ' 关闭Excel的警告提示
    Application.DisplayAlerts = False
    
    ' 计算要复制的行数
    Dim rowCount As Long
    rowCount = sourceEnd - sourceStart + 1
    
    ' 选择源数据行
    Dim sourceRange As Range
    Set sourceRange = ws.Range("A" & sourceStart & ":R" & sourceEnd)
    
    ' 复制源数据行（保持格式）
    sourceRange.Copy
    
    ' 在目标位置插入足够的空白行
    ws.Rows(targetRow & ":" & (targetRow + rowCount - 1)).Insert Shift:=xlDown
    
    ' 在目标位置插入格式（但不包含内容）
    ws.Range("A" & targetRow).PasteSpecial xlPasteFormats
    
    ' 清除剪贴板
    Application.CutCopyMode = False
    
    ' 清除目标行所有单元格的内容（保留格式）
    ws.Range("A" & targetRow & ":R" & (targetRow + rowCount - 1)).ClearContents
    
    ' 保留A列和B列的合并单元格状态
    If Not IsEmpty(ws.Range("A" & sourceStart).MergeArea.Cells(1, 1).Value) Then
        ws.Range("A" & targetRow & ":A" & (targetRow + 1)).Merge
    End If
    
    ws.Range("B" & targetRow & ":B" & (targetRow + 1)).Merge
    
    ' 恢复Excel的警告提示
    Application.DisplayAlerts = True
    
    Debug.Print "复制空白数据行: 从行 " & sourceStart & "-" & sourceEnd & " 到行 " & targetRow
End Sub

' ▼▼▼▼▼ 创建空白数据行 ▼▼▼▼▼
' 功能：在指定位置创建空白数据行（两行高）
' 参数：ws - 工作表对象
'       targetRow - 目标位置行
Private Sub CreateEmptyRow(ws As Worksheet, targetRow As Long)
    ' 关闭Excel的警告提示
    Application.DisplayAlerts = False
    
    ' 插入两行
    ws.Rows(targetRow & ":" & (targetRow + 1)).Insert Shift:=xlDown
    
    ' 设置基本格式
    With ws.Range("A" & targetRow & ":R" & (targetRow + 1))
        .Font.Name = "Times New Roman"
        .Font.Size = 10
        .Interior.ColorIndex = xlNone
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With
    
    ' 合并B列的两行
    ws.Range("B" & targetRow & ":B" & (targetRow + 1)).Merge
    
    ' 恢复Excel的警告提示
    Application.DisplayAlerts = True
    
    Debug.Print "创建空白数据行: 行 " & targetRow & "-" & (targetRow + 1)
End Sub

' ▼▼▼▼▼ 检测并删除空白数据行 ▼▼▼▼▼
' 功能：检测工作表二中的空白数据行并删除，保持数据组结构完整
' 参数：无
' 返回：Long - 删除的空白行数量
Function DetectAndRemoveEmptyRows() As Long
    Dim ws As Worksheet               ' 工作表对象
    Dim i As Long, j As Long          ' 循环变量
    Dim headerStarts() As Long        ' 存储所有新表头起始行
    Dim headerCount As Long           ' 新表头计数
    Dim headerText As String          ' 单元格文本
    Dim cell As Range                 ' 单元格对象
    Dim dataGroupStarts() As Long     ' 每个数据组的起始行
    Dim dataGroupEnds() As Long       ' 每个数据组的结束行
    Dim rowsToDelete As New Collection ' 需要删除的行集合
    Dim dataRowEmpty As Boolean       ' 数据行是否为空标志
    Dim row As Long                   ' 当前检查的行
    Dim deletedCount As Long          ' 删除的行数计数
    Dim originalDisplayAlerts As Boolean ' 保存原始警告提示设置
    
    ' 初始化计数器
    deletedCount = 0
    
    ' 保存原始警告提示设置
    originalDisplayAlerts = Application.DisplayAlerts
    
    ' 关闭Excel的警告提示
    Application.DisplayAlerts = False
    
    ' 关闭屏幕更新以提高性能
    Application.ScreenUpdating = False
    
    ' 获取工作表二
    On Error Resume Next
    Set ws = ThisWorkbook.Worksheets(2)
    On Error GoTo 0
    
    ' 确保工作表二存在
    If ws Is Nothing Then
        MsgBox "工作表二不存在！", vbExclamation
        Application.DisplayAlerts = originalDisplayAlerts
        Application.ScreenUpdating = True
        DetectAndRemoveEmptyRows = 0
        Exit Function
    End If
    
    ' 首先识别所有新表头位置
    headerCount = 0
    ReDim headerStarts(1 To 100)      ' 假设最多100个表头
    
    For i = 1 To ws.UsedRange.Rows.count
        For j = 1 To ws.UsedRange.Columns.count
            Set cell = ws.Cells(i, j)
            
            ' 跳过空单元格
            If Not IsEmpty(cell.Value) Then
                ' 获取单元格文本并检查是否包含目标文本
                headerText = CStr(cell.Value)
                
                ' 检查是否包含"指令编号"和"Order No."
                If InStr(headerText, "指令编号") > 0 And InStr(headerText, "Order No.") > 0 Then
                    ' 检查该单元格是否为合并单元格
                    If cell.MergeCells Then
                        ' 检查合并单元格是否为3行高
                        If cell.MergeArea.Rows.count = 3 Then
                            headerCount = headerCount + 1
                            headerStarts(headerCount) = i
                            Debug.Print "找到新表头 #" & headerCount & " 在行: " & i
                        End If
                    End If
                End If
            End If
        Next j
    Next i
    
    ' 确保至少有一个表头
    If headerCount < 1 Then
        MsgBox "未找到任何表头！", vbExclamation
        Application.DisplayAlerts = originalDisplayAlerts
        Application.ScreenUpdating = True
        DetectAndRemoveEmptyRows = 0
        Exit Function
    End If
    
    ' 定义数组存储每个数据组的起始行和结束行
    ReDim dataGroupStarts(1 To headerCount)
    ReDim dataGroupEnds(1 To headerCount)
    
    ' 计算每个数据组的起始行和结束行
    For i = 1 To headerCount
        ' 数据组起始行是表头后第9行
        dataGroupStarts(i) = headerStarts(i) + 9
        
        ' 数据组结束行是下一个表头前一行，如果是最后一个表头则到工作表末尾
        If i < headerCount Then
            dataGroupEnds(i) = headerStarts(i + 1) - 1
        Else
            ' 最后一个表头的数据区域到工作表的已用区域末尾行
            dataGroupEnds(i) = ws.UsedRange.Rows.count
        End If
        
        Debug.Print "数据组 #" & i & ": 起始行 " & dataGroupStarts(i) & "，结束行 " & dataGroupEnds(i)
    Next i
    
    ' 检查每个数据组中的数据行是否为空
    For i = 1 To headerCount
        ' 从数据组起始行开始，每次检查两行（一个数据行）
        For row = dataGroupStarts(i) To dataGroupEnds(i) Step 2
            ' 确保不会超出范围
            If row + 1 <= dataGroupEnds(i) Then
                ' 检查这两行（一个数据行）是否为空
                dataRowEmpty = True
                
                ' 检查C列到R列的所有单元格是否为空
                For j = 3 To 18  ' C=3, R=18
                    ' 检查这两行的单元格
                    If Not IsEmpty(ws.Cells(row, j).Value) Or Not IsEmpty(ws.Cells(row + 1, j).Value) Then
                        dataRowEmpty = False
                        Exit For
                    End If
                Next j
                
                ' 如果数据行为空，并且不是B列有内容的行（工序名称行），则标记为删除
                If dataRowEmpty Then
                    ' 检查B列是否为空（合并单元格）
                    If IsEmpty(ws.Range("B" & row).MergeArea.Cells(1, 1).Value) Then
                        ' 将这两行添加到删除集合中
                        rowsToDelete.Add row
                        Debug.Print "标记空白数据行删除: 行 " & row & "-" & (row + 1)
                    End If
                End If
            End If
        Next row
    Next i
    
    ' 从后向前删除标记的行（避免删除后索引变化）
    If rowsToDelete.count > 0 Then
        ' 将集合转换为数组以便排序
        Dim rowsArray() As Long
        ReDim rowsArray(1 To rowsToDelete.count)
        
        For i = 1 To rowsToDelete.count
            rowsArray(i) = rowsToDelete(i)
        Next i
        
                 ' 对数组进行降序排序
         Dim temp As Long
         For i = 1 To UBound(rowsArray)
             For j = i + 1 To UBound(rowsArray)
                 If rowsArray(i) < rowsArray(j) Then
                     temp = rowsArray(i)
                     rowsArray(i) = rowsArray(j)
                     rowsArray(j) = temp
                 End If
             Next j
         Next i
        
        ' 从后向前删除行
        For i = 1 To UBound(rowsArray)
            ws.Rows(rowsArray(i) & ":" & (rowsArray(i) + 1)).Delete Shift:=xlUp
            deletedCount = deletedCount + 1
            Debug.Print "删除空白数据行: 行 " & rowsArray(i) & "-" & (rowsArray(i) + 1)
        Next i
    End If
    
    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    
    ' 恢复Excel的警告提示
    Application.DisplayAlerts = originalDisplayAlerts
    
    ' 返回删除的行数
    DetectAndRemoveEmptyRows = deletedCount
End Function

' ▼▼▼▼▼ 调整分页预览线框 ▼▼▼▼▼
' 功能：调整分页预览线框至数据组末尾，以显示完整页面
' 参数：无
Sub AdjustPageBreaks()
    Dim ws As Worksheet               ' 工作表对象
    Dim i As Long, j As Long          ' 循环变量
    Dim headerStarts() As Long        ' 存储所有新表头起始行
    Dim headerCount As Long           ' 新表头计数
    Dim headerText As String          ' 单元格文本
    Dim cell As Range                 ' 单元格对象
    Dim dataGroupStarts() As Long     ' 每个数据组的起始行
    Dim dataGroupEnds() As Long       ' 每个数据组的结束行
    Dim originalDisplayAlerts As Boolean ' 保存原始警告提示设置
    
    ' 保存原始警告提示设置
    originalDisplayAlerts = Application.DisplayAlerts
    
    ' 关闭Excel的警告提示
    Application.DisplayAlerts = False
    
    ' 关闭屏幕更新以提高性能
    Application.ScreenUpdating = False
    
    ' 获取工作表二
    On Error Resume Next
    Set ws = ThisWorkbook.Worksheets(2)
    On Error GoTo 0
    
    ' 确保工作表二存在
    If ws Is Nothing Then
        MsgBox "工作表二不存在！", vbExclamation
        Application.DisplayAlerts = originalDisplayAlerts
        Application.ScreenUpdating = True
        Exit Sub
    End If
    
    ' 首先识别所有新表头位置
    headerCount = 0
    ReDim headerStarts(1 To 100)      ' 假设最多100个表头
    
    For i = 1 To ws.UsedRange.Rows.count
        For j = 1 To ws.UsedRange.Columns.count
            Set cell = ws.Cells(i, j)
            
            ' 跳过空单元格
            If Not IsEmpty(cell.Value) Then
                ' 获取单元格文本并检查是否包含目标文本
                headerText = CStr(cell.Value)
                
                ' 检查是否包含"指令编号"和"Order No."
                If InStr(headerText, "指令编号") > 0 And InStr(headerText, "Order No.") > 0 Then
                    ' 检查该单元格是否为合并单元格
                    If cell.MergeCells Then
                        ' 检查合并单元格是否为3行高
                        If cell.MergeArea.Rows.count = 3 Then
                            headerCount = headerCount + 1
                            headerStarts(headerCount) = i
                            Debug.Print "找到新表头 #" & headerCount & " 在行: " & i
                        End If
                    End If
                End If
            End If
        Next j
    Next i
    
    ' 确保至少有一个表头
    If headerCount < 1 Then
        MsgBox "未找到任何表头！", vbExclamation
        Application.DisplayAlerts = originalDisplayAlerts
        Application.ScreenUpdating = True
        Exit Sub
    End If
    
    ' 定义数组存储每个数据组的起始行和结束行
    ReDim dataGroupStarts(1 To headerCount)
    ReDim dataGroupEnds(1 To headerCount)
    
    ' 计算每个数据组的起始行和结束行
    For i = 1 To headerCount
        ' 数据组起始行是表头后第9行
        dataGroupStarts(i) = headerStarts(i) + 9
        
        ' 数据组结束行是下一个表头前一行，如果是最后一个表头则到工作表末尾
        If i < headerCount Then
            dataGroupEnds(i) = headerStarts(i + 1) - 1
        Else
            ' 最后一个表头的数据区域到工作表的已用区域末尾行
            dataGroupEnds(i) = ws.UsedRange.Rows.count
        End If
        
        Debug.Print "数据组 #" & i & ": 起始行 " & dataGroupStarts(i) & "，结束行 " & dataGroupEnds(i)
    Next i
    
    ' 清除现有的所有分页符
    ws.ResetAllPageBreaks
    
    ' 为每个数据组设置分页符
    For i = 1 To headerCount
        ' 如果不是最后一个数据组，在数据组结束行之后添加分页符
        If i < headerCount Then
            ' 在数据组结束行之后添加分页符
            ws.HPageBreaks.Add Before:=ws.Rows(dataGroupEnds(i) + 1)
            Debug.Print "在行 " & (dataGroupEnds(i) + 1) & " 添加分页符"
        End If
    Next i
    
    ' 设置打印区域以包含所有数据
    If headerCount > 0 Then
        ' 设置打印区域从第一个表头开始到最后一个数据组结束
        ws.PageSetup.PrintArea = "$A$" & headerStarts(1) & ":$R$" & dataGroupEnds(headerCount)
        Debug.Print "设置打印区域: $A$" & headerStarts(1) & ":$R$" & dataGroupEnds(headerCount)
    End If
    
    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    
    ' 恢复Excel的警告提示
    Application.DisplayAlerts = originalDisplayAlerts
    
    MsgBox "分页预览线框调整完成！", vbInformation
End Sub














